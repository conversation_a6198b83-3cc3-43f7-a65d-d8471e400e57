package org.zstack.storage.primary.sharedblock;

import org.zstack.core.Platform;
import org.zstack.core.upgrade.GrayVersion;
import org.zstack.header.HasThreadContext;
import org.zstack.header.agent.ReloadableCommand;
import org.zstack.header.core.validation.Validation;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.kvm.KVMAgentCommands;
import org.zstack.storage.volume.VolumeErrors;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.zstack.core.Platform.operr;
import static org.zstack.storage.primary.sharedblock.SharedBlockKvmBackend.buildQcow2Options;
import static org.zstack.storage.primary.sharedblock.SharedBlockKvmBackend.getFailIfNoPath;

public class SharedBlockKvmCommands {
    public static final String PING_PATH = "/sharedblock/ping";
    @AcquireExVgLock
    public static final String CONNECT_PATH = "/sharedblock/connect";
    @AcquireExVgLock
    public static final String DISCONNECT_PATH = "/sharedblock/disconnect";
    @AcquireExVgLock
    public static final String CREATE_VOLUME_FROM_CACHE_PATH = "/sharedblock/createrootvolume";
    @AcquireExVgLock
    public static final String DELETE_BITS_PATH = "/sharedblock/bits/delete";
    public static final String CREATE_TEMPLATE_FROM_VOLUME_PATH = "/sharedblock/createtemplatefromvolume";
    public static final String ESTIMATE_TEMPLATE_SIZE_PATH = "/sharedblock/estimatetemplatesize";
    public static final String CREATE_IMAGE_CACHE_FROM_VOLUME_PATH = "/sharedblock/createimagecachefromvolume";
    public static final String UPLOAD_BITS_TO_SFTP_BACKUPSTORAGE_PATH = "/sharedblock/sftp/upload";
    public static final String DOWNLOAD_BITS_FROM_SFTP_BACKUPSTORAGE_PATH = "/sharedblock/sftp/download";
    public static final String REVERT_VOLUME_FROM_SNAPSHOT_PATH = "/sharedblock/volume/revertfromsnapshot";
    public static final String MERGE_SNAPSHOT_PATH = "/sharedblock/snapshot/merge";
    public static final String EXTEND_MERGE_TARGET_PATH = "/sharedblock/snapshot/extendmergetarget";
    public static final String EXTEND_MIGRATE_TARGET_PATH = "/sharedblock/volume/extendmigratetarget";
    public static final String OFFLINE_MERGE_SNAPSHOT_PATH = "/sharedblock/snapshot/offlinemerge";
    @AcquireExVgLock
    public static final String CREATE_EMPTY_VOLUME_PATH = "/sharedblock/volume/createempty";
    @AcquireExVgLock
    public static final String CREATE_DATA_VOLUME_WITH_BACKING_PATH = "/sharedblock/volume/createwithbacking";
    public static final String CHECK_BITS_PATH = "/sharedblock/bits/check";
    public static final String GET_VOLUME_SIZE_PATH = "/sharedblock/volume/getsize";
    public static final String BATCH_GET_VOLUME_SIZE_PATH = "/sharedblock/volume/batchgetsize";
    public static final String CHANGE_VOLUME_ACTIVE_PATH = "/sharedblock/volume/active";
    public static final String CONVERT_IMAGE_TO_VOLUME = "/sharedblock/image/tovolume";
    public static final String CHECK_DISKS_PATH = "/sharedblock/disks/check";
    @AcquireExVgLock
    public static final String ADD_SHARED_BLOCK = "/sharedblock/disks/add";
    @AcquireExVgLock
    public static final String RESIZE_VOLUME_PATH = "/sharedblock/volume/resize";
    public static final String MIGRATE_DATA_PATH = "/sharedblock/volume/migrate";
    public static final String GET_BLOCK_DEVICES_PATH = "/sharedblock/blockdevices";
    public static final String KVM_HA_CANCEL_SELF_FENCER = "/ha/sharedblock/cancelselffencer";
    public static final String KVM_HA_SETUP_SELF_FENCER = "/ha/sharedblock/setupselffencer";
    public static final String DOWNLOAD_BITS_FROM_KVM_HOST_PATH = "/sharedblock/kvmhost/download";
    public static final String CANCEL_DOWNLOAD_BITS_FROM_KVM_HOST_PATH = "/sharedblock/kvmhost/download/cancel";
    public static final String GET_BACKING_CHAIN_PATH = "/sharedblock/volume/backingchain";
    public static final String CONVERT_VOLUME_PROVISIONING_PATH = "/sharedblock/volume/convertprovisioning";
    public static final String CONFIG_FILTER_PATH = "/sharedblock/disks/filter";
    public static final String CONVERT_VOLUME_FORMAT_PATH = "/sharedblock/volume/convertformat";
    public static final String GET_DOWNLOAD_BITS_FROM_KVM_HOST_PROGRESS_PATH = "/sharedblock/kvmhost/download/progress";
    public static final String SHRINK_SNAPSHOT_PATH = "/sharedblock/snapshot/shrink";
    public static final String GET_QCOW2_HASH_VALUE_PATH = "/sharedblock/getqcow2hash";
    public static final String CHECK_STATE_PATH = "/sharedblock/vgstate/check";

    public static class AgentCmd extends KVMAgentCommands.PrimaryStorageCommand {
        @GrayVersion(value = "5.0.0")
        public String vgUuid;
        @GrayVersion(value = "5.0.0")
        public String hostUuid;
        @GrayVersion(value = "5.0.0")
        public String provisioning;
        @GrayVersion(value = "5.0.0")
        public Map<String, Object> addons;

        public Map<String, Object> getAddons() {
            if (addons == null) {
                addons = new HashMap<>();
            }
            return addons;
        }

        public void setAddons(Map<String, Object> addons) {
            this.addons = addons;
        }
    }

    public static class Qcow2Cmd extends AgentCmd {
        @GrayVersion(value = "5.0.0")
        public String qcow2Options = buildQcow2Options();
    }

    public static class AgentRsp {
        public boolean success = true;
        public String error;
        public Long totalCapacity;
        public Long availableCapacity;
        public List<LunCapacity> lunCapacities;

        public void setError(String error) {
            success = false;
            this.error = error;
        }

        protected ErrorCode buildErrorCode() {
            if (success) {
                return null;
            }
            return operr("operation error, because:%s", error);
        }
    }

    public static class CheckLockCmd extends AgentCmd {
        @GrayVersion(value = "5.0.0")
        public List<String> vgUuids;
    }

    public static class CheckDisksCmd extends AgentCmd {
        @GrayVersion(value = "5.0.0")
        public List<String> sharedBlockUuids;
        @GrayVersion(value = "5.0.0")
        public boolean rescan = false;
        @GrayVersion(value = "5.0.0")
        public boolean rescan_scsi = false;
        @GrayVersion(value = "5.0.0")
        public boolean failIfNoPath = getFailIfNoPath();
    }

    public static class ConfigFilterCmd extends AgentCmd {
        @GrayVersion(value = "5.0.0")
        public List<String> allSharedBlockUuids;
    }

    public static class ConnectCmd extends AgentCmd {
        @GrayVersion(value = "5.0.0")
        public List<String> sharedBlockUuids;
        @GrayVersion(value = "5.0.0")
        public String hostId;
        @GrayVersion(value = "5.0.0")
        public boolean forceWipe = false;
        @GrayVersion(value = "5.0.0")
        public boolean enableLvmetad = false;
        @GrayVersion(value = "5.0.0")
        public List<String> allSharedBlockUuids;
        @GrayVersion(value = "5.0.0")
        public Long ioTimeout = 40l;
        @GrayVersion(value = "5.0.0")
        public Long maxActualSizeFactor = 3l;
        @GrayVersion(value = "5.0.0")
        public boolean isFirst;
    }

    public static class ConnectRsp extends AgentRsp {
        public boolean isFirst = false;
        public String hostId;
        public String vgLvmUuid;
        public String hostUuid;
    }

    public static class CheckLockRsp extends AgentRsp {
        public Map<String, String> failedVgs;
    }

    public static class ActivateRsp extends AgentRsp {
        public boolean inUse;
        public ErrorCode buildErrorCode() {
            if (inUse) {
                return Platform.err(VolumeErrors.VOLUME_IN_USE, error);
            }
            return super.buildErrorCode();
        }
    }

    public static class DisonnectCmd extends AgentCmd {
        @GrayVersion(value = "5.0.0")
        public boolean stopServices;
    }

    public static class AddDiskCmd extends AgentCmd {
        @GrayVersion(value = "5.0.0")
        public String diskUuid;
        @GrayVersion(value = "5.0.0")
        public List<String> allSharedBlockUuids;
        @GrayVersion(value = "5.0.0")
        public boolean forceWipe = false;
        @GrayVersion(value = "5.0.0")
        public boolean onlyGenerateFilter = false;
    }

    public static class CreateVolumeFromCacheCmd extends AgentCmd implements ProvisionSharedBlockVolumeCmd, HasThreadContext {
        @GrayVersion(value = "5.0.0")
        public String templatePathInCache;
        @GrayVersion(value = "5.0.0")
        public String installPath;
        @GrayVersion(value = "5.0.0")
        public String volumeUuid;
        @GrayVersion(value = "5.0.0")
        public long virtualSize;

        @Override
        public String getVolumeUuid() {
            return volumeUuid;
        }

        @Override
        public String getInstallPath() {
            return installPath;
        }
    }

    public static class CreateVolumeFromCacheRsp extends AgentRsp {
        public Long actualSize;
        public Long size;
    }
    public static class DeleteBitsCmd extends AgentCmd {
        @GrayVersion(value = "5.0.0")
        public String path;
        @GrayVersion(value = "5.0.0")
        public boolean folder = false;
        @GrayVersion(value = "5.0.0")
        public String issueDiscards;
    }

    public static class DeleteTag extends AgentCmd {
        @GrayVersion(value = "5.0.0")
        public String tag;
    }

    public static class CreateTemplateFromVolumeCmd extends AgentCmd implements HasThreadContext {
        @GrayVersion(value = "5.0.0")
        public String installPath;
        @GrayVersion(value = "5.0.0")
        public String volumePath;
        @GrayVersion(value = "5.0.0")
        public boolean sharedVolume = false;
        @GrayVersion(value = "5.0.0")
        public boolean compareQcow2 = false;
    }

    public static class CreateTemplateFromVolumeRsp extends AgentRsp {
        public long actualSize;
        public long size;
    }

    public static class EstimateTemplateSizeCmd extends AgentCmd {
        @GrayVersion(value = "5.0.0")
        public String volumePath;
    }

    public static class EstimateTemplateSizeRsp extends AgentRsp {
        public long actualSize;
        public long size;
    }

    public static class CreateImageCacheFromVolumeCmd extends AgentCmd implements HasThreadContext {
        @GrayVersion(value = "5.0.0")
        public String installPath;
        @GrayVersion(value = "5.0.0")
        public String volumePath;
        @GrayVersion(value = "5.0.0")
        public boolean incremental;
        @GrayVersion(value = "5.0.0")
        public boolean compareQcow2 = false;
    }

    public static class CreateImageCacheFromVolumeRsp extends AgentRsp {
        public long actualSize;
        public long size;
    }

    public static class SftpUploadBitsCmd extends AgentCmd implements HasThreadContext{
        @GrayVersion(value = "5.0.0")
        public String primaryStorageInstallPath;
        @GrayVersion(value = "5.0.0")
        public String backupStorageInstallPath;
        @GrayVersion(value = "5.0.0")
        public String hostname;
        @GrayVersion(value = "5.0.0")
        public String username;
        @GrayVersion(value = "5.0.0")
        public String sshKey;
        @GrayVersion(value = "5.0.0")
        public int sshPort;
    }

    public static class SftpDownloadBitsCmd extends AgentCmd {
        @GrayVersion(value = "5.0.0")
        public String sshKey;
        @GrayVersion(value = "5.0.0")
        public int sshPort;
        @GrayVersion(value = "5.0.0")
        public int lockType = LvmlockdLockingType.SHARE.getValue();
        @GrayVersion(value = "5.0.0")
        public String hostname;
        @GrayVersion(value = "5.0.0")
        public String username;
        @GrayVersion(value = "5.0.0")
        public String backupStorageInstallPath;
        @GrayVersion(value = "5.0.0")
        public String primaryStorageInstallPath;
    }

    public static class RevertVolumeFromSnapshotCmd extends AgentCmd implements ProvisionSharedBlockVolumeCmd {
        @GrayVersion(value = "5.0.0")
        public String snapshotInstallPath;
        @GrayVersion(value = "5.0.0")
        public String volumeUuid;
        @GrayVersion(value = "5.0.0")
        public String installPath;

        @Override
        public String getVolumeUuid() {
            return volumeUuid;
        }

        @Override
        public String getInstallPath() {
            return installPath;
        }
    }

    public static class RevertVolumeFromSnapshotRsp extends AgentRsp {
        @Validation
        public String newVolumeInstallPath;

        @Validation
        public long size;
    }

    public static class MergeSnapshotCmd extends AgentCmd implements HasThreadContext {
        @GrayVersion(value = "5.0.0")
        public String volumeUuid;
        @GrayVersion(value = "5.0.0")
        public String snapshotInstallPath;
        @GrayVersion(value = "5.0.0")
        public String workspaceInstallPath;
    }

    public static class MergeSnapshotRsp extends AgentRsp {
        public long actualSize;
        public long size;
    }

    public static class ExtendMergeTargetCmd extends AgentCmd {
        @GrayVersion(value = "5.0.0")
        public String srcPath;
        @GrayVersion(value = "5.0.0")
        public String destPath;
        @GrayVersion(value = "5.0.0")
        public boolean fullRebase;
        @GrayVersion(value = "5.0.0")
        public String volumeUuid;
    }

    public static class ExtendMigrateTargetCmd extends AgentCmd implements ProvisionSharedBlockVolumeCmd {
        @GrayVersion(value = "5.0.0")
        public String destPath;
        @GrayVersion(value = "5.0.0")
        public String volumeUuid;
        @GrayVersion(value = "5.0.0")
        public long requiredSize;

        @Override
        public String getVolumeUuid() {
            return volumeUuid;
        }

        @Override
        public String getInstallPath() {
            return destPath;
        }
    }

    public static class ExtendMergeTargetRsp extends AgentRsp {
    }

    public static class ExtendMigrateTargetRsp extends AgentRsp {
    }

    public static class OfflineMergeSnapshotCmd extends AgentCmd implements ProvisionSharedBlockVolumeCmd, HasThreadContext {
        @GrayVersion(value = "5.0.0")
        public String srcPath;
        @GrayVersion(value = "5.0.0")
        public String destPath;
        @GrayVersion(value = "5.0.0")
        public boolean fullRebase;
        @GrayVersion(value = "5.0.0")
        public boolean sharedVolume = false;
        @GrayVersion(value = "5.0.0")
        public String volumeUuid;

        @Override
        public String getVolumeUuid() {
            return volumeUuid;
        }

        @Override
        public String getInstallPath() {
            return srcPath;
        }
    }

    public static class CreateEmptyVolumeCmd extends AgentCmd implements ProvisionSharedBlockVolumeCmd, HasThreadContext {
        @GrayVersion(value = "5.0.0")
        public String installPath;
        @GrayVersion(value = "5.0.0")
        public long size;
        @GrayVersion(value = "5.0.0")
        public String name;
        @GrayVersion(value = "5.0.0")
        public String volumeUuid;
        @GrayVersion(value = "5.0.0")
        public String volumeFormat;
        public String backingFile;

        @Override
        public String getVolumeUuid() {
            return volumeUuid;
        }

        @Override
        public String getInstallPath() {
            return installPath;
        }
    }

    public static class CreateEmptyVolumeRsp extends AgentRsp {
        public Long actualSize;
        public Long size;
    }

    public static class CreateDataVolumeWithBackingCmd extends AgentCmd implements ProvisionSharedBlockVolumeCmd, HasThreadContext {
        @GrayVersion(value = "5.0.0")
        public String templatePathInCache;
        @GrayVersion(value = "5.0.0")
        public String installPath;
        @GrayVersion(value = "5.0.0")
        public String volumeUuid;

        @Override
        public String getVolumeUuid() {
            return volumeUuid;
        }

        @Override
        public String getInstallPath() {
            return installPath;
        }
    }

    public static class CreateDataVolumeWithBackingRsp extends AgentRsp {
        public long actualSize;
        public long size;
    }

    public static class CheckBitsCmd extends AgentCmd {
        @GrayVersion(value = "5.0.0")
        public String path;
    }

    public static class CheckBitsRsp extends AgentRsp {
        public boolean existing;
    }

    public static class GetVolumeSizeCmd extends AgentCmd {
        @GrayVersion(value = "5.0.0")
        public String volumeUuid;
        @GrayVersion(value = "5.0.0")
        public String installPath;
    }

    public static class GetBatchVolumeSizeCmd extends AgentCmd {
        @GrayVersion(value = "5.0.0")
        public Map<String, String> volumeUuidInstallPaths;
    }

    public static class GetVolumeSizeRsp extends AgentRsp {
        public Long actualSize;
        public Long size;
    }

    public static class GetBatchVolumeSizeRsp extends AgentRsp {
        public Map<String, Long> actualSizes = new HashMap<>();
    }

    public static class ActiveVolumeCmd extends AgentCmd {
        @GrayVersion(value = "5.0.0")
        public String installPath;
        @GrayVersion(value = "5.0.0")
        public int lockType;
        @GrayVersion(value = "5.0.0")
        public boolean recursive = true;
        @GrayVersion(value = "5.0.0")
        public boolean killProcess = false;
    }

    public static class ConvertImageToVolumeCmd extends AgentCmd {
        @GrayVersion(value = "5.0.0")
        public String primaryStorageInstallPath;
    }

    public static class ResizeVolumeCmd extends AgentCmd implements ProvisionSharedBlockVolumeCmd {
        @GrayVersion(value = "5.0.0")
        public String installPath;
        @GrayVersion(value = "5.0.0")
        public long size;
        @GrayVersion(value = "5.0.0")
        public boolean force = false;
        @GrayVersion(value = "5.0.0")
        public boolean live = false;
        @GrayVersion(value = "5.0.0")
        public String volumeUuid;

        @Override
        public String getVolumeUuid() {
            return volumeUuid;
        }

        @Override
        public String getInstallPath() {
            return installPath;
        }
    }

    public static class ResizeVolumeRsp extends AgentRsp {
        public long size;
    }

    public static class ConvertVolumeFormatCmd extends AgentCmd {
        @GrayVersion(value = "5.0.0")
        public String srcFormat;
        @GrayVersion(value = "5.0.0")
        public String dstFormat;
        @GrayVersion(value = "5.0.0")
        public String installPath;
    }

    public static class MigrateDataCmd extends AgentCmd implements HasThreadContext {
        @GrayVersion(value = "5.0.0")
        public List<SharedBlockMigrateVolumeStruct> migrateVolumeStructs;
        @GrayVersion(value = "5.0.0")
        public String volumePath;
    }

    public static class GetBlockDevicesCmd extends AgentCmd {
    }

    public static class GetBlockDevicesRsp extends AgentRsp {
        List<BlockDeviceStruct> blockDevices;

        public List<BlockDeviceStruct> getBlockDevices() {
            return blockDevices;
        }

        public void setBlockDevices(List<BlockDeviceStruct> blockDevices) {
            this.blockDevices = blockDevices;
        }
    }

    public static class KvmSetupSelfFencerCmd extends AgentCmd  {
        @GrayVersion(value = "5.0.0")
        public long interval;
        @GrayVersion(value = "5.0.0")
        public int maxAttempts;
        @GrayVersion(value = "5.0.0")
        public int storageCheckerTimeout;
        @GrayVersion(value = "5.0.0")
        public boolean fail_if_no_path = SharedBlockGlobalConfig.FAIL_IF_MULTIPATH_NO_PATH.value().equals("true");
        @GrayVersion(value = "5.0.0")
        public boolean checkIo = SharedBlockGlobalConfig.CHECK_IO_FENCER.value().equals("true");
        @GrayVersion(value = "5.0.0")
        public String strategy;
        @GrayVersion(value = "5.0.0")
        public List<String> fencers;
    }

    public static class KvmCancelSelfFencerCmd extends AgentCmd {
    }

    public static class DownloadBitsFromKVMHostRsp extends AgentRsp {
        public String format;
    }

    public static class DownloadBitsFromKVMHostCmd extends AgentCmd implements ReloadableCommand {
        @GrayVersion(value = "5.0.0")
        private String hostname;
        @GrayVersion(value = "5.0.0")
        private String username;
        @GrayVersion(value = "5.0.0")
        private String sshKey;
        @GrayVersion(value = "5.0.0")
        private int sshPort;
        @GrayVersion(value = "5.0.0")
        private int lockType = LvmlockdLockingType.SHARE.getValue();
        // it's file path on kvm host actually
        @GrayVersion(value = "5.0.0")
        private String backupStorageInstallPath;
        @GrayVersion(value = "5.0.0")
        private String primaryStorageInstallPath;
        @GrayVersion(value = "5.0.0")
        private Long bandWidth;
        @GrayVersion(value = "5.0.0")
        private String identificationCode;

        public String getHostname() {
            return hostname;
        }

        public void setHostname(String hostname) {
            this.hostname = hostname;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getSshKey() {
            return sshKey;
        }

        public void setSshKey(String sshKey) {
            this.sshKey = sshKey;
        }

        public int getSshPort() {
            return sshPort;
        }

        public void setSshPort(int sshPort) {
            this.sshPort = sshPort;
        }

        public int getLockType() {
            return lockType;
        }

        public void setLockType(int lockType) {
            this.lockType = lockType;
        }

        public String getBackupStorageInstallPath() {
            return backupStorageInstallPath;
        }

        public void setBackupStorageInstallPath(String backupStorageInstallPath) {
            this.backupStorageInstallPath = backupStorageInstallPath;
        }

        public String getPrimaryStorageInstallPath() {
            return primaryStorageInstallPath;
        }

        public void setPrimaryStorageInstallPath(String primaryStorageInstallPath) {
            this.primaryStorageInstallPath = primaryStorageInstallPath;
        }

        public Long getBandWidth() {
            return bandWidth;
        }

        public void setBandWidth(Long bandWidth) {
            this.bandWidth = bandWidth;
        }

        @Override
        public void setIdentificationCode(String identificationCode) {
            this.identificationCode = identificationCode;
        }
    }

    public static class CancelDownloadBitsFromKVMHostCmd extends AgentCmd {
        @GrayVersion(value = "5.0.0")
        private int lockType = LvmlockdLockingType.SHARE.getValue();
        @GrayVersion(value = "5.0.0")
        private String primaryStorageInstallPath;


        public String getPrimaryStorageInstallPath() {
            return primaryStorageInstallPath;
        }

        public void setPrimaryStorageInstallPath(String primaryStorageInstallPath) {
            this.primaryStorageInstallPath = primaryStorageInstallPath;
        }
    }

    public static class GetDownloadBitsFromKVMHostProgressCmd extends AgentCmd {
        @GrayVersion(value = "5.0.0")
        public List<String> volumePaths;
    }

    public static class GetDownloadBitsFromKVMHostProgressRsp extends AgentRsp {
        public long totalSize;
    }

    public static class GetBackingChainCmd extends AgentCmd {
        @GrayVersion(value = "5.0.0")
        public String volumeUuid;
        @GrayVersion(value = "5.0.0")
        public String installPath;
        @GrayVersion(value = "5.0.0")
        public boolean containSelf = true;
    }

    public static class GetBackingChainRsp extends AgentRsp {
        public List<String> backingChain;
        public long totalSize;
    }

    public static class ConvertVolumeProvisioningCmd extends AgentCmd implements ProvisionSharedBlockVolumeCmd {
        @GrayVersion(value = "5.0.0")
        public String provisioningStrategy;
        @GrayVersion(value = "5.0.0")
        public String installPath;
        @GrayVersion(value = "5.0.0")
        public String volumeUuid;

        public String getProvisioningStrategy() {
            return provisioningStrategy;
        }

        public void setProvisioningStrategy(String provisioningStrategy) {
            this.provisioningStrategy = provisioningStrategy;
        }

        @Override
        public String getInstallPath() {
            return installPath;
        }

        public void setInstallPath(String installPath) {
            this.installPath = installPath;
        }

        @Override
        public String getVolumeUuid() {
            return volumeUuid;
        }

        public void setVolumeUuid(String volumeUuid) {
            this.volumeUuid = volumeUuid;
        }
    }

    public static class ConvertVolumeProvisioningRsp extends AgentRsp {
        public Long actualSize;
    }

    public static class ShrinkSnapshotCmd extends AgentCmd {
        @GrayVersion(value = "5.0.0")
        public String installPath;
    }

    public static class ShrinkSnapshotRsp extends AgentRsp {
        public Long oldSize;
        public Long size;
    }

    public static class GetQcow2HashValueCmd extends AgentCmd {
        @GrayVersion(value = "5.0.0")
        private String installPath;

        public String getInstallPath() {
            return installPath;
        }

        public void setInstallPath(String installPath) {
            this.installPath = installPath;
        }
    }

    public static class GetQcow2HashValueRsp extends AgentRsp {
        private String hashValue;

        public String getHashValue() {
            return hashValue;
        }

        public void setHashValue(String hashValue) {
            this.hashValue = hashValue;
        }
    }
}
