package org.zstack.storage.primary.sharedblock;

import com.google.common.collect.ConcurrentHashMultiset;
import com.google.common.collect.Multiset;
import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.core.asyncbatch.While;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.MessageSafe;
import org.zstack.core.componentloader.PluginRegistry;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.core.db.SQL;
import org.zstack.header.AbstractService;
import org.zstack.header.core.WhileDoneCompletion;
import org.zstack.header.core.ReturnValueCompletion;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.errorcode.ErrorCodeList;
import org.zstack.header.host.HostStatus;
import org.zstack.header.host.HostVO;
import org.zstack.header.host.HostVO_;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.Message;
import org.zstack.identity.AccountManager;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

import java.util.ArrayList;
import java.util.List;

/**
 * Create by weiwang at 2018/7/13
 */
public class SharedBlockManagerImpl extends AbstractService {
    private static final CLogger logger = Utils.getLogger(SharedBlockManagerImpl.class);
    @Autowired
    private CloudBus bus;
    @Autowired
    private PluginRegistry pluginRgty;
    @Autowired
    private AccountManager acntMgr;
    @Autowired
    private DatabaseFacade dbf;

    @Override
    @MessageSafe
    public void handleMessage(Message msg) {
        if (msg instanceof APIMessage) {
            handleApiMessage((APIMessage) msg);
        } else {
            handleLocalMessage(msg);
        }
    }

    private void handleLocalMessage(Message msg) {
        bus.dealWithUnknownMessage(msg);
    }

    private void handleApiMessage(APIMessage msg) {
        if (msg instanceof APIGetSharedBlockCandidateMsg) {
            handle((APIGetSharedBlockCandidateMsg) msg);
        } else {
            bus.dealWithUnknownMessage(msg);
        }
    }

    private void handle(APIGetSharedBlockCandidateMsg msg) {
        APIGetSharedBlockCandidateReply reply = new APIGetSharedBlockCandidateReply();
        reply.setResults(new ArrayList<>());

        List<HostVO> hostVOS = Q.New(HostVO.class)
                .eq(HostVO_.clusterUuid, msg.getClusterUuid())
                .eq(HostVO_.status, HostStatus.Connected)
                .list();

        if (hostVOS == null || hostVOS.isEmpty()) {
            bus.reply(msg, reply);
            return;
        }

        Multiset<BlockDeviceStruct> devices = ConcurrentHashMultiset.create();
        new While<>(hostVOS).step((hostVO, whileCompletion) -> {
            SharedBlockKvmCommands.GetBlockDevicesCmd cmd = new SharedBlockKvmCommands.GetBlockDevicesCmd();
            cmd.hostUuid = hostVO.getUuid();
            new KvmAgentCommandDispatcher(null, hostVO.getUuid()).go(SharedBlockKvmCommands.GET_BLOCK_DEVICES_PATH, cmd, SharedBlockKvmCommands.GetBlockDevicesRsp.class, new ReturnValueCompletion<SharedBlockKvmCommands.GetBlockDevicesRsp>(whileCompletion) {
                @Override
                public void success(SharedBlockKvmCommands.GetBlockDevicesRsp returnValue) {
                    devices.addAll(returnValue.blockDevices);
                    whileCompletion.done();
                }

                @Override
                public void fail(ErrorCode errorCode) {
                    whileCompletion.done();
                }
            });
        }, 5).run(new WhileDoneCompletion(msg) {
            @Override
            public void done(ErrorCodeList errorCodeList) {
                logger.debug("got sharedblock device info from all hosts, now remove attached devices");
                List<String> existsSharedBlockWwids = Q.New(SharedBlockVO.class).select(SharedBlockVO_.diskUuid).listValues();
                List<String> existsVmAttachWwids = SQL.New("select lun.wwid from ScsiLunVmInstanceRefVO ref, ScsiLunVO lun" +
                        " where lun.uuid = ref.scsiLunUuid", String.class).list();

                for (BlockDeviceStruct s : devices) {
                    Long count = devices.stream().filter(d -> d.equals(s)).count();

                    if (count < hostVOS.size() || reply.getResults().contains(new SharedBlockCandidateStruct(s))) {
                        continue;
                    }

                    if (existsSharedBlockWwids.contains(s.wwid) || existsSharedBlockWwids.contains(s.wwn)) {
                        continue;
                    }

                    if (existsVmAttachWwids.contains(s.wwid) || existsVmAttachWwids.contains(s.wwn)) {
                        continue;
                    }

                    reply.getResults().add(new SharedBlockCandidateStruct(s));
                }

                logger.debug("sharedblock devices calculate done, return result");
                bus.reply(msg, reply);
            }
        });
    }

    @Override
    public String getId() {
        return bus.makeLocalServiceId(SharedBlockConstants.SERVICE_ID);
    }

    @Override
    public boolean start() {
        return true;
    }

    @Override
    public boolean stop() {
        return true;
    }
}
