<?xml version="1.0" encoding="UTF-8"?>
<service xmlns="http://zstack.org/schema/zstack">
    <id>SdnController</id>
    <interceptor>SdnControllerApiInterceptor</interceptor>

    <message>
        <name>org.zstack.sdnController.header.APIAddSdnControllerMsg</name>
    </message>

    <message>
        <name>org.zstack.sdnController.header.APIRemoveSdnControllerMsg</name>
    </message>

    <message>
        <name>org.zstack.sdnController.header.APIUpdateSdnControllerMsg</name>
    </message>

    <message>
        <name>org.zstack.sdnController.header.APISdnControllerAddHostMsg</name>
    </message>

    <message>
        <name>org.zstack.sdnController.header.APISdnControllerChangeHostMsg</name>
    </message>

    <message>
        <name>org.zstack.sdnController.header.APISdnControllerRemoveHostMsg</name>
    </message>

    <message>
        <name>org.zstack.sdnController.header.APIChangeSdnControllerMsg</name>
    </message>

    <message>
        <name>org.zstack.sdnController.header.APIReconnectSdnControllerMsg</name>
    </message>

    <message>
        <name>org.zstack.sdnController.header.APIQuerySdnControllerMsg</name>
        <serviceId>query</serviceId>
    </message>

    <message>
        <name>org.zstack.network.hostNetworkInterface.APIQueryPhysicalSwitchMsg</name>
        <serviceId>query</serviceId>
    </message>
</service>