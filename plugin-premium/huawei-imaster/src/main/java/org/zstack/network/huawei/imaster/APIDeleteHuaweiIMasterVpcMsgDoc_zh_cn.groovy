package org.zstack.network.huawei.imaster

import org.zstack.network.huawei.imaster.APIDeleteHuaweiIMasterVpcEvent

doc {
    title "DeleteHuaweiIMasterVpc"

    category "huawei.imaster"

    desc """删除Huawei IMaster VPC"""

    rest {
        request {
			url "DELETE /v1/sdn-controller/huawei-imaster/vpcs/{uuid}"

			header (Authorization: 'OAuth the-session-uuid')

            clz APIDeleteHuaweiIMasterVpcMsg.class

            desc """"""
            
			params {

				column {
					name "uuid"
					enclosedIn ""
					desc "资源的UUID，唯一标示该资源"
					location "url"
					type "String"
					optional false
					since "5.3.28"
				}
				column {
					name "sdnControllerUuid"
					enclosedIn ""
					desc ""
					location "body"
					type "String"
					optional true
					since "5.3.28"
				}
				column {
					name "deleteMode"
					enclosedIn ""
					desc "删除模式(Permissive / Enforcing，Permissive)"
					location "body"
					type "String"
					optional true
					since "5.3.28"
				}
				column {
					name "systemTags"
					enclosedIn ""
					desc "系统标签"
					location "body"
					type "List"
					optional true
					since "5.3.28"
				}
				column {
					name "userTags"
					enclosedIn ""
					desc "用户标签"
					location "body"
					type "List"
					optional true
					since "5.3.28"
				}
			}
        }

        response {
            clz APIDeleteHuaweiIMasterVpcEvent.class
        }
    }
}