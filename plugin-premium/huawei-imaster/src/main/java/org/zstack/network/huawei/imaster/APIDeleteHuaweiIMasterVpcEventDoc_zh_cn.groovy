package org.zstack.network.huawei.imaster

import org.zstack.header.errorcode.ErrorCode

doc {

	title "删除Huawei IMaster VPC事件"

	field {
		name "success"
		desc ""
		type "boolean"
		since "5.3.28"
	}
	ref {
		name "error"
		path "org.zstack.network.huawei.imaster.APIDeleteHuaweiIMasterVpcEvent.error"
		desc "错误码，若不为null，则表示操作失败, 操作成功时该字段为null",false
		type "ErrorCode"
		since "5.3.28"
		clz ErrorCode.class
	}
}
