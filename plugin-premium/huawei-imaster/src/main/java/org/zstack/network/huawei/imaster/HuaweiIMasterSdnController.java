package org.zstack.network.huawei.imaster;

import com.aliyuncs.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.http.HttpMethod;
import org.zstack.compute.vm.MacOperator;
import org.zstack.core.CoreGlobalProperty;
import org.zstack.core.Platform;
import org.zstack.core.asyncbatch.While;
import org.zstack.core.cascade.CascadeConstant;
import org.zstack.core.cascade.CascadeFacade;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.core.db.SQL;
import org.zstack.core.workflow.FlowChainBuilder;
import org.zstack.header.core.Completion;
import org.zstack.header.core.NopeCompletion;
import org.zstack.header.core.ReturnValueCompletion;
import org.zstack.header.core.WhileDoneCompletion;
import org.zstack.header.core.workflow.*;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.errorcode.ErrorCodeList;
import org.zstack.header.exception.CloudRuntimeException;
import org.zstack.header.host.HostInventory;
import org.zstack.header.host.HostVO;
import org.zstack.header.host.HostVO_;
import org.zstack.header.message.APIDeleteMessage;
import org.zstack.header.message.Message;
import org.zstack.header.message.MessageReply;
import org.zstack.header.network.l2.L2NetworkConstant;
import org.zstack.header.network.l2.L2NetworkDeletionMsg;
import org.zstack.header.network.l2.L2NetworkInventory;
import org.zstack.header.network.l3.IpRangeInventory;
import org.zstack.header.network.l3.L3NetworkInventory;
import org.zstack.header.network.l3.L3NetworkVO;
import org.zstack.header.network.l3.SdnControllerL3;
import org.zstack.network.hostNetworkInterface.*;
import org.zstack.network.hostNetworkInterface.lldp.entity.HostNetworkInterfaceLldpRefVO;
import org.zstack.network.hostNetworkInterface.lldp.entity.HostNetworkInterfaceLldpRefVO_;
import org.zstack.network.hostNetworkInterface.lldp.entity.HostNetworkInterfaceLldpVO;
import org.zstack.network.hostNetworkInterface.lldp.entity.HostNetworkInterfaceLldpVO_;
import org.zstack.network.l2.vxlan.vxlanNetwork.L2VxlanNetworkInventory;
import org.zstack.sdnController.*;
import org.zstack.sdnController.header.*;
import org.zstack.utils.Utils;
import org.zstack.utils.gson.JSONObjectUtil;
import org.zstack.utils.logging.CLogger;
import org.zstack.utils.network.IPv6Constants;
import org.zstack.utils.network.NetworkUtils;

import java.util.*;
import java.util.stream.Collectors;

import static java.util.Arrays.asList;
import static org.zstack.core.Platform.argerr;
import static org.zstack.core.Platform.operr;
import static org.zstack.network.huawei.imaster.HuaweiIMasterNceFabricCommands.*;

@Configurable(preConstruction = true, autowire = Autowire.BY_TYPE)
public class HuaweiIMasterSdnController implements SdnController, SdnControllerL2, SdnControllerL3 {
    private static final CLogger logger = Utils.getLogger(HuaweiIMasterSdnController.class);

    @Autowired
    private DatabaseFacade dbf;
    @Autowired
    CloudBus bus;
    @Autowired
    private CascadeFacade casf;

    private HuaweiIMasterSdnControllerVO self;
    private HuaweiIMasterHelper helper = new HuaweiIMasterHelper();
    private HardwareVxlanHelper vxlanHelper = new HardwareVxlanHelper();

    public HuaweiIMasterSdnController(HuaweiIMasterSdnControllerVO self) {
        this.self = self;
    }

    private Map<String, String> getHeaders() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Accept", "application/json");
        headers.put("Accept-Language", "en-US");

        return headers;
    }

    private Map<String, String> getHeaders(String token) {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Accept", "application/json");
        headers.put("Accept-Language", "en-US");
        headers.put("X-ACCESS-TOKEN", token);

        return headers;
    }
    
    @Override
    public void handleMessage(SdnControllerMessage msg) {
        if (msg instanceof APICreateHuaweiIMasterVRouterMsg) {
            handle((APICreateHuaweiIMasterVRouterMsg) msg);
        } else if (msg instanceof APIDeleteHuaweiIMasterVRouterMsg) {
            handle((APIDeleteHuaweiIMasterVRouterMsg) msg);
        } else if (msg instanceof APIPullHuaweiIMasterControllerMsg) {
            handle((APIPullHuaweiIMasterControllerMsg)msg);
        } else if (msg instanceof APIDeleteHuaweiIMasterTenantMsg) {
            handle((APIDeleteHuaweiIMasterTenantMsg)msg);
        } else if (msg instanceof SdnControllerPingMsg) {
            handle((SdnControllerPingMsg)msg);
        } else if (msg instanceof HuaweiIMasterFabricDeletionMsg) {
            handle((HuaweiIMasterFabricDeletionMsg)msg);
        } else if (msg instanceof HuaweiIMasterTenantDeletionMsg) {
            handle((HuaweiIMasterTenantDeletionMsg)msg);
        } else if (msg instanceof HuaweiIMasterVpcDeletionMsg) {
            handle((HuaweiIMasterVpcDeletionMsg)msg);
        } else if (msg instanceof HuaweiIMasterVRouterDeletionMsg) {
            handle((HuaweiIMasterVRouterDeletionMsg)msg);
        } else {
            bus.dealWithUnknownMessage((Message) msg);
        }
    }

    private void handle(HuaweiIMasterVRouterDeletionMsg msg) {
        HuaweiIMasterVRouterDeletionReply reply = new HuaweiIMasterVRouterDeletionReply();
        doDeleteHuaweiIMasterLogicalRouter(Collections.singletonList(msg.getvRouterUuid()), new Completion(msg) {
            @Override
            public void success() {
                SQL.New(HuaweiIMasterVRouterVO.class)
                        .eq(HuaweiIMasterVRouterVO_.uuid, msg.getvRouterUuid()).delete();
                bus.reply(msg, reply);
            }

            @Override
            public void fail(ErrorCode errorCode) {
                reply.setError(errorCode);
                bus.reply(msg, reply);
            }
        });
    }

    private void handle(HuaweiIMasterVpcDeletionMsg msg) {
        HuaweiIMasterVpcDeletionReply reply = new HuaweiIMasterVpcDeletionReply();
        List<String> l2Uuids = Q.New(HardwareL2VxlanNetworkVO.class).select(HardwareL2VxlanNetworkVO_.uuid).listValues();
        if (l2Uuids.isEmpty()) {
            SQL.New(HuaweiIMasterVpcVO.class)
                    .eq(HuaweiIMasterVpcVO_.uuid, msg.getVpcUuid()).delete();
            bus.reply(msg, reply);
            return;
        }

        new While<>(l2Uuids).each((uuid, wcopl) -> {
            String vpcUuid = HuaweiIMasterHelper.getL2NetworkVpcId(uuid);
            if (vpcUuid == null || !vpcUuid.equals(msg.vpcUuid)) {
                wcopl.done();
                return;
            }

            L2NetworkDeletionMsg dmsg = new L2NetworkDeletionMsg();
            dmsg.setL2NetworkUuid(uuid);
            bus.makeTargetServiceIdByResourceUuid(dmsg, L2NetworkConstant.SERVICE_ID, uuid);
            bus.send(dmsg, new CloudBusCallBack(dmsg) {
                @Override
                public void run(MessageReply reply) {
                    if (!reply.isSuccess()) {
                        logger.debug(String.format("delete l2 network[uuid:%s] failed, because: %s",
                                uuid, reply.getError().getDetails()));
                    }
                    wcopl.done();
                }
            });
        }).run(new WhileDoneCompletion(msg) {
            @Override
            public void done(ErrorCodeList errorCodeList) {
                SQL.New(HuaweiIMasterVpcVO.class)
                        .eq(HuaweiIMasterVpcVO_.uuid, msg.getVpcUuid()).delete();
                bus.reply(msg, reply);
            }
        });
    }

    private void handle(HuaweiIMasterFabricDeletionMsg msg) {
        // ordinary resource is delete in cascade
        HuaweiIMasterFabricDeletionReply reply = new HuaweiIMasterFabricDeletionReply();
        SQL.New(HuaweiIMasterFabricVO.class)
                .eq(HuaweiIMasterFabricVO_.uuid, msg.getFabricUuid()).delete();
        bus.reply(msg, reply);
    }

    private void handle(HuaweiIMasterTenantDeletionMsg msg) {
        // ordinary resource is delete in cascade
        HuaweiIMasterTenantDeletionReply reply = new HuaweiIMasterTenantDeletionReply();
        SQL.New(HuaweiIMasterTenantVO.class)
                .eq(HuaweiIMasterTenantVO_.uuid, msg.getTenantUuid()).delete();
        bus.reply(msg, reply);
    }

    private void handle(APIPullHuaweiIMasterControllerMsg msg) {
        APIPullHuaweiIMasterControllerEvent event = new APIPullHuaweiIMasterControllerEvent(msg.getId());
        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName(String.format("init-huawei-imaster-%s", self.getIp()));
        chain.then(new Flow() {
            String __name__ = "get_huawei_imaster_token";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                getHuaweiIMasterToken(new ReturnValueCompletion<String>(trigger) {
                    @Override
                    public void success(String returnValue) {
                        data.put("token", returnValue);
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                String token = (String) data.get("token");
                deleteHuaweiIMasterToken(token, new Completion(trigger) {
                    @Override
                    public void success() {
                        trigger.rollback();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.rollback();
                    }
                });
            }
        }).then(new NoRollbackFlow() {
            String __name__ = "get_huawei_imaster_fabric";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String token = (String) data.get("token");
                getHuaweiIMasterControllerFabric(token, new ReturnValueCompletion<List<HuaweiIMasterFabricVO>>(trigger) {
                    @Override
                    public void success(List<HuaweiIMasterFabricVO> returnValue) {
                        List<HuaweiIMasterFabricVO> newFabricVOS = new ArrayList<>();
                        for (HuaweiIMasterFabricVO vo : returnValue) {
                            if (Q.New(HuaweiIMasterFabricVO.class).eq(HuaweiIMasterFabricVO_.uuid, vo.getUuid()).isExists()) {
                                continue;
                            }

                            vo.setSdnControllerUuid(self.getUuid());
                            vo.setAccountUuid(msg.getSession().getAccountUuid());
                            newFabricVOS.add(vo);
                        }
                        if (!newFabricVOS.isEmpty()) {
                            dbf.persistCollection(newFabricVOS);
                        }

                        List<String> fabricIds = returnValue.stream().map(vo ->
                                vo.getUuid()).collect(Collectors.toList());
                        SQL.New(HuaweiIMasterFabricVO.class).in(HuaweiIMasterFabricVO_.uuid, fabricIds)
                                .set(HuaweiIMasterFabricVO_.state, SdnControllerTableState.Enabled).update();
                        SQL.New(HuaweiIMasterFabricVO.class).notIn(HuaweiIMasterFabricVO_.uuid, fabricIds)
                                .set(HuaweiIMasterFabricVO_.state, SdnControllerTableState.Disabled).update();

                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        //ingore errors
                        logger.debug(String.format("pull fabric from huawei imaster failed because %s", errorCode.getDetails()));
                        trigger.next();
                    }
                });
            }
        }).then(new NoRollbackFlow() {
            String __name__ = "get_huawei_imaster_tenant";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String token = (String) data.get("token");
                getHuaweiIMasterControllerTenant(token, new ReturnValueCompletion<List<HuaweiIMasterNceFabricCommands.TenantStruct>>(trigger) {
                    @Override
                    public void success(List<HuaweiIMasterNceFabricCommands.TenantStruct> returnValue) {
                        List<HuaweiIMasterTenantVO> newTenantVOS = new ArrayList<>();
                        for (HuaweiIMasterNceFabricCommands.TenantStruct tenant : returnValue) {
                            if (Q.New(HuaweiIMasterTenantVO.class).eq(HuaweiIMasterTenantVO_.uuid, tenant.id).isExists()) {
                                continue;
                            }

                            HuaweiIMasterTenantVO vo = tenant.toHuaweiIMasterTenantVO();
                            vo.setSdnControllerUuid(self.getUuid());
                            vo.setAccountUuid(msg.getSession().getAccountUuid());
                            newTenantVOS.add(vo);
                        }
                        if (!newTenantVOS.isEmpty()) {
                            dbf.persistCollection(newTenantVOS);
                        }

                        List<String> tenantUuids = returnValue.stream().map(v ->
                                HuaweiIMasterHelper.huaweiIMasterIdToZStackUuid(v.id)).collect(Collectors.toList());
                        SQL.New(HuaweiIMasterTenantVO.class).in(HuaweiIMasterTenantVO_.uuid, tenantUuids)
                                .set(HuaweiIMasterTenantVO_.state, SdnControllerTableState.Enabled).update();
                        SQL.New(HuaweiIMasterTenantVO.class).notIn(HuaweiIMasterTenantVO_.uuid, tenantUuids)
                                .set(HuaweiIMasterTenantVO_.state, SdnControllerTableState.Disabled).update();
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        //ingore errors
                        logger.debug(String.format("pull tenant from huawei imaster failed because %s", errorCode.getDetails()));
                        trigger.next();
                    }
                });
            }
        }).then(new NoRollbackFlow() {
            String __name__ = "get_huawei_imaster_vpc";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String token = (String) data.get("token");
                getHuaweiIMasterControllerVpc(token,
                        new ReturnValueCompletion<List<HuaweiIMasterVpcVO>>(trigger) {
                            @Override
                            public void success(List<HuaweiIMasterVpcVO> returnValue) {
                                List<HuaweiIMasterVpcVO> newVpcVOS = new ArrayList<>();
                                for (HuaweiIMasterVpcVO vo : returnValue) {
                                    if (Q.New(HuaweiIMasterVpcVO.class).eq(HuaweiIMasterVpcVO_.uuid, vo.getUuid()).isExists()) {
                                        continue;
                                    }

                                    vo.setSdnControllerUuid(self.getUuid());
                                    vo.setAccountUuid(msg.getSession().getAccountUuid());
                                    newVpcVOS.add(vo);
                                }

                                if (!newVpcVOS.isEmpty()) {
                                    dbf.persistCollection(newVpcVOS);
                                }

                                List<String> vpcUuids = returnValue.stream().map(vo ->
                                        vo.getUuid()).collect(Collectors.toList());
                                SQL.New(HuaweiIMasterVpcVO.class).in(HuaweiIMasterVpcVO_.uuid, vpcUuids)
                                        .set(HuaweiIMasterVpcVO_.state, SdnControllerTableState.Enabled).update();
                                SQL.New(HuaweiIMasterVpcVO.class).notIn(HuaweiIMasterVpcVO_.uuid, vpcUuids)
                                        .set(HuaweiIMasterVpcVO_.state, SdnControllerTableState.Disabled).update();
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                //ingore errors
                                logger.debug(String.format("pull vpc from huawei imaster failed because %s", errorCode.getDetails()));
                                trigger.next();
                            }
                        });
            }
        }).then(new NoRollbackFlow() {
            String __name__ = "delete_huawei_imaster_token";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String token = (String) data.get("token");
                deleteHuaweiIMasterToken(token, new Completion(trigger) {
                    @Override
                    public void success() {
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        //ignore delete toke error
                        trigger.next();
                    }
                });
            }
        }).done(new FlowDoneHandler(msg) {
            @Override
            public void handle(Map data) {
                List<HuaweiIMasterSdnControllerInventory> invs = new ArrayList<>();
                HuaweiIMasterSdnControllerVO vo = dbf.findByUuid(msg.getSdnControllerUuid(), HuaweiIMasterSdnControllerVO.class);
                invs.add(HuaweiIMasterSdnControllerInventory.valueOf(vo));
                event.setInventories(invs);
                bus.publish(event);
            }
        }).error(new FlowErrorHandler(msg) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                event.setError(errCode);
                bus.publish(event);
            }
        }).start();


    }

    private void handle(APICreateHuaweiIMasterVRouterMsg msg) {
        APICreateHuaweiIMasterVRouterEvent event = new APICreateHuaweiIMasterVRouterEvent(msg.getId());

        HuaweiIMasterVpcVO vpc = dbf.findByUuid(msg.getHuaweiVpcUuid(), HuaweiIMasterVpcVO.class);
        HuaweiIMasterVRouterVO vo = new HuaweiIMasterVRouterVO();
        if (msg.getResourceUuid() != null) {
            vo.setUuid(msg.getResourceUuid());
        } else {
            vo.setUuid(Platform.getUuid());
        }
        vo.setName(msg.getName());
        vo.setDescription(msg.getDescription());
        vo.setTenantId(vpc.getTenantId());
        vo.setLogicalNetworkId(msg.getHuaweiVpcUuid());
        vo.setAccountUuid(msg.getSession().getAccountUuid());
        vo.setFabricUuid(vpc.getFabricId());
        vo.setSdnControllerUuid(self.getUuid());
        vo.setState(SdnControllerTableState.Enabled);

        List<Flow> flows = new ArrayList<>();
        flows.add(new NoRollbackFlow() {
            String __name__ = "create-logical-router";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String token = (String) data.get("token");
                createHuaweiIMasterLogicalRouter(token,
                        HuaweiIMasterVRouterInventory.valueOf(vo), new Completion(trigger) {
                    @Override
                    public void success() {
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }
        });

        FlowChain chain = getHuaweiIMasterFlowChain(
                String.format("create-huawei-imaster-logical-router"), flows, new Completion(msg) {
            @Override
            public void success() {
                HuaweiIMasterVRouterVO fvo = dbf.persistAndRefresh(vo);
                event.setInventory(HuaweiIMasterVRouterInventory.valueOf(fvo));
                bus.publish(event);
            }

            @Override
            public void fail(ErrorCode errorCode) {
                event.setError(errorCode);
                bus.publish(event);
            }
        });

        chain.start();
    }

    private void doDeleteHuaweiIMasterLogicalRouter(List<String> vRouterUuids, Completion completion) {
        List<Flow> flows = new ArrayList<>();
        flows.add(new NoRollbackFlow() {
            String __name__ = "delete-logical-router";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String token = (String) data.get("token");
                new While<>(vRouterUuids).each((uuid, wcompl) -> {
                    deleteHuaweiIMasterLogicalRouter(token, uuid, new Completion(trigger) {
                        @Override
                        public void success() {
                            wcompl.done();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            wcompl.addError(errorCode);
                            wcompl.allDone();
                        }
                    });
                }).run(new WhileDoneCompletion(trigger) {
                    @Override
                    public void done(ErrorCodeList errorCodeList) {
                        if (errorCodeList.getCauses().isEmpty()) {
                            trigger.next();
                        } else {
                            trigger.fail(errorCodeList);
                        }
                    }
                });
            }
        });

        FlowChain chain = getHuaweiIMasterFlowChain(
                String.format("delete-huawei-imaster-logical-router"), flows, new Completion(completion) {
                    @Override
                    public void success() {
                        completion.success();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        completion.fail(errorCode);
                    }
                });

        chain.start();
    }

    private void handle(APIDeleteHuaweiIMasterTenantMsg msg) {
        APIDeleteHuaweiIMasterTenantEvent event = new APIDeleteHuaweiIMasterTenantEvent(msg.getId());

        final String issuer = HuaweiIMasterTenantVO.class.getSimpleName();
        HuaweiIMasterTenantVO vo = dbf.findByUuid(msg.getUuid(), HuaweiIMasterTenantVO.class);
        final List<HuaweiIMasterTenantInventory> ctx = asList(HuaweiIMasterTenantInventory.valueOf(vo));
        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName(String.format("delete-huawei-imaster-tenant-%s-%s", msg.getUuid(), vo.getName()));
        if (msg.getDeletionMode() == APIDeleteMessage.DeletionMode.Permissive) {
            chain.then(new NoRollbackFlow() {
                @Override
                public void run(final FlowTrigger trigger, Map data) {
                    casf.asyncCascade(CascadeConstant.DELETION_CHECK_CODE, issuer, ctx, new Completion(trigger) {
                        @Override
                        public void success() {
                            trigger.next();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            trigger.fail(errorCode);
                        }
                    });
                }
            }).then(new NoRollbackFlow() {
                @Override
                public void run(final FlowTrigger trigger, Map data) {
                    casf.asyncCascade(CascadeConstant.DELETION_DELETE_CODE, issuer, ctx, new Completion(trigger) {
                        @Override
                        public void success() {
                            trigger.next();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            trigger.fail(errorCode);
                        }
                    });
                }
            });
        } else {
            chain.then(new NoRollbackFlow() {
                @Override
                public void run(final FlowTrigger trigger, Map data) {
                    casf.asyncCascade(CascadeConstant.DELETION_FORCE_DELETE_CODE, issuer, ctx, new Completion(trigger) {
                        @Override
                        public void success() {
                            trigger.next();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            trigger.fail(errorCode);
                        }
                    });
                }
            });
        }
        chain.done(new FlowDoneHandler(msg) {
            @Override
            public void handle(Map data) {
                casf.asyncCascadeFull(CascadeConstant.DELETION_CLEANUP_CODE, issuer, ctx, new NopeCompletion());
                bus.publish(event);
            }
        }).error(new FlowErrorHandler(msg) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                event.setError(errCode);
                bus.publish(event);
            }
        }).start();
    }

    private void handle(APIDeleteHuaweiIMasterVRouterMsg msg) {
        APIDeleteHuaweiIMasterVRouterEvent event = new APIDeleteHuaweiIMasterVRouterEvent(msg.getId());

        final String issuer = HuaweiIMasterVRouterVO.class.getSimpleName();
        HuaweiIMasterVRouterVO vo = dbf.findByUuid(msg.getUuid(), HuaweiIMasterVRouterVO.class);
        final List<HuaweiIMasterVRouterInventory> ctx = asList(HuaweiIMasterVRouterInventory.valueOf(vo));
        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName(String.format("delete-huawei-imaster-vrouter-%s-%s", msg.getUuid(), vo.getName()));
        if (msg.getDeletionMode() == APIDeleteMessage.DeletionMode.Permissive) {
            chain.then(new NoRollbackFlow() {
                @Override
                public void run(final FlowTrigger trigger, Map data) {
                    casf.asyncCascade(CascadeConstant.DELETION_CHECK_CODE, issuer, ctx, new Completion(trigger) {
                        @Override
                        public void success() {
                            trigger.next();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            trigger.fail(errorCode);
                        }
                    });
                }
            }).then(new NoRollbackFlow() {
                @Override
                public void run(final FlowTrigger trigger, Map data) {
                    casf.asyncCascade(CascadeConstant.DELETION_DELETE_CODE, issuer, ctx, new Completion(trigger) {
                        @Override
                        public void success() {
                            trigger.next();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            trigger.fail(errorCode);
                        }
                    });
                }
            });
        } else {
            chain.then(new NoRollbackFlow() {
                @Override
                public void run(final FlowTrigger trigger, Map data) {
                    casf.asyncCascade(CascadeConstant.DELETION_FORCE_DELETE_CODE, issuer, ctx, new Completion(trigger) {
                        @Override
                        public void success() {
                            trigger.next();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            trigger.fail(errorCode);
                        }
                    });
                }
            });
        }
        chain.done(new FlowDoneHandler(msg) {
            @Override
            public void handle(Map data) {
                casf.asyncCascadeFull(CascadeConstant.DELETION_CLEANUP_CODE, issuer, ctx, new NopeCompletion());
                bus.publish(event);
            }
        }).error(new FlowErrorHandler(msg) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                event.setError(errCode);
                bus.publish(event);
            }
        }).start();
    }

    void handle(SdnControllerPingMsg msg) {
        SdnControllerPingReply reply = new SdnControllerPingReply();

        getHuaweiIMasterToken(new ReturnValueCompletion<String>(msg) {
            @Override
            public void success(String returnValue) {
                deleteHuaweiIMasterToken(returnValue, new Completion(msg) {
                    @Override
                    public void success() {
                        bus.reply(msg, reply);
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        //ignore delete toke error
                        bus.reply(msg, reply);
                    }
                });
            }

            @Override
            public void fail(ErrorCode errorCode) {
                reply.setError(errorCode);
                bus.reply(msg, reply);
            }
        });
    }

    @Override
    @SdnControllerLog
    public void preInitSdnController(APIAddSdnControllerMsg msg, Completion completion) {
        completion.success();
    }

    @Override
    public void createSdnControllerDb(APIAddSdnControllerMsg msg, SdnControllerVO vo, Completion completion) {
        HuaweiIMasterSdnControllerVO hwVo = new HuaweiIMasterSdnControllerVO(vo);
        dbf.persist(hwVo);
        completion.success();
    }

    @Override
    public void deleteSdnControllerDb(SdnControllerVO vo) {
        dbf.removeByPrimaryKey(vo.getUuid(), HuaweiIMasterSdnControllerVO.class);
    }

    @Override
    @SdnControllerLog
    public void initSdnController(APIAddSdnControllerMsg msg, Completion completion) {
        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName(String.format("init-huawei-imaster-%s", self.getIp()));
        chain.then(new Flow() {
            String __name__ = "get_huawei_imaster_token";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                getHuaweiIMasterToken(new ReturnValueCompletion<String>(trigger) {
                    @Override
                    public void success(String returnValue) {
                        data.put("token", returnValue);
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                String token = (String) data.get("token");
                deleteHuaweiIMasterToken(token, new Completion(trigger) {
                    @Override
                    public void success() {
                        trigger.rollback();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.rollback();
                    }
                });
            }
        }).then(new NoRollbackFlow() {
            String __name__ = "get_huawei_imaster_system_info";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String token = (String) data.get("token");
                getHuaweiIMasterSystemInfo(token, new ReturnValueCompletion<HuaweiIMasterNceFabricCommands.GetSystemInfoRsp>(trigger) {
                    @Override
                    public void success(HuaweiIMasterNceFabricCommands.GetSystemInfoRsp returnValue) {
                        SQL.New(HuaweiIMasterSdnControllerVO.class)
                                .eq(HuaweiIMasterSdnControllerVO_.uuid, self.getUuid())
                                .set(HuaweiIMasterSdnControllerVO_.vendorVersion, returnValue.productVersion);
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }
        }).then(new Flow() {
            String __name__ = "get_huawei_imaster_fabric";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String token = (String) data.get("token");
                getHuaweiIMasterControllerFabric(token, new ReturnValueCompletion<List<HuaweiIMasterFabricVO>>(trigger) {
                    @Override
                    public void success(List<HuaweiIMasterFabricVO> returnValue) {
                        List<HuaweiIMasterFabricVO> fabricVOS = new ArrayList<>();
                        for (HuaweiIMasterFabricVO fabric : returnValue) {
                            fabric.setSdnControllerUuid(self.getUuid());
                            fabric.setAccountUuid(msg.getSession().getAccountUuid());
                            fabricVOS.add(fabric);
                        }

                        if (!fabricVOS.isEmpty()) {
                            dbf.persistCollection(fabricVOS);
                        }

                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                SQL.New(HuaweiIMasterFabricVO.class).eq(HuaweiIMasterFabricVO_.sdnControllerUuid, self.getUuid()).delete();
                trigger.rollback();
            }
        }).then(new Flow() {
            String __name__ = "get_huawei_imaster_tenant";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String token = (String) data.get("token");
                getHuaweiIMasterControllerTenant(token, new ReturnValueCompletion<List<HuaweiIMasterNceFabricCommands.TenantStruct>>(trigger) {
                    @Override
                    public void success(List<HuaweiIMasterNceFabricCommands.TenantStruct> returnValue) {
                        logger.debug("tenant struct: " + JSONObjectUtil.toJsonString(returnValue));
                        List<HuaweiIMasterTenantVO> tenantVOS = new ArrayList<>();
                        for (HuaweiIMasterNceFabricCommands.TenantStruct tenant : returnValue) {
                            HuaweiIMasterTenantVO vo = tenant.toHuaweiIMasterTenantVO();
                            vo.setSdnControllerUuid(self.getUuid());
                            vo.setAccountUuid(msg.getSession().getAccountUuid());
                            tenantVOS.add(vo);
                        }

                        logger.debug("tenant vo: " + JSONObjectUtil.toJsonString(tenantVOS));
                        if (!tenantVOS.isEmpty()) {
                            dbf.persistCollection(tenantVOS);
                        }
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                SQL.New(HuaweiIMasterTenantVO.class).eq(HuaweiIMasterTenantVO_.sdnControllerUuid, self.getUuid()).delete();
                trigger.rollback();
            }
        }).then(new Flow() {
            String __name__ = "get_huawei_imaster_phy_switch";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String token = (String) data.get("token");

                getHuaweiIMasterControllerSwitches(token,
                        new ReturnValueCompletion<List<HuaweiIMasterNceFabricCommands.SwitchStruct>>(trigger) {
                    @Override
                    public void success(List<HuaweiIMasterNceFabricCommands.SwitchStruct> returnValue) {
                        List<PhysicalSwitchVO> newSwitchVOS = new ArrayList<>();
                        List<PhysicalSwitchVO> updateSwitchVOS = new ArrayList<>();
                        for (HuaweiIMasterNceFabricCommands.SwitchStruct hswitch : returnValue) {
                            PhysicalSwitchVO vo = hswitch.toPhysicalSwitchVO();
                            vo.setAccountUuid(msg.getSession().getAccountUuid());
                            vo.setSdnControllerUuid(self.getUuid());
                            PhysicalSwitchVO oldSwitchVO = dbf.findByUuid(vo.getUuid(), PhysicalSwitchVO.class);
                            if (oldSwitchVO != null) {
                                oldSwitchVO.copyFromAnother(vo);
                                updateSwitchVOS.add(oldSwitchVO);
                            } else {
                                newSwitchVOS.add(vo);
                            }
                        }

                        if (!newSwitchVOS.isEmpty()) {
                            dbf.persistCollection(newSwitchVOS);
                        }
                        if (!updateSwitchVOS.isEmpty()) {
                            dbf.updateCollection(updateSwitchVOS);
                        }

                        data.put("switchVOS", newSwitchVOS);
                        data.put("oldSwitchVOS", updateSwitchVOS);
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                List<PhysicalSwitchVO> switchVOS = (List<PhysicalSwitchVO>) data.get("switchVOS");
                if (switchVOS != null && !switchVOS.isEmpty()) {
                    SQL.New(PhysicalSwitchVO.class)
                            .in(PhysicalSwitchVO_.uuid, switchVOS.stream().map(PhysicalSwitchVO::getUuid).collect(Collectors.toList()))
                            .delete();
                }
                trigger.rollback();
            }
        }).then(new Flow() {
            String __name__ = "get_huawei_imaster_switch_ports";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                List<PhysicalSwitchVO> switchVos = new ArrayList<>();
                List<PhysicalSwitchVO> newSwitchVOS = (List<PhysicalSwitchVO>) data.get("switchVOS");
                if (newSwitchVOS != null && !newSwitchVOS.isEmpty()) {
                    switchVos.addAll(newSwitchVOS);
                }

                List<PhysicalSwitchVO> oldSwitchVOS = (List<PhysicalSwitchVO>) data.get("oldSwitchVOS");
                if (oldSwitchVOS != null && !oldSwitchVOS.isEmpty()) {
                    switchVos.addAll(oldSwitchVOS);
                }

                if (switchVos.isEmpty()) {
                    trigger.fail(argerr("there is no switch attached to sdn controller"));
                    return;
                }

                Map<String, String> switchIdToNameMap = switchVos.stream().collect(
                        Collectors.toMap(vo -> vo.getUuid(), vo -> vo.getName()));

                List<String> switchIds = switchVos.stream().map(vo ->
                        HuaweiIMasterHelper.zStackUuidToHuaweiIMasterId(vo.getUuid())).collect(Collectors.toList());
                String token = (String) data.get("token");
                getHuaweiIMasterControllerSwitchPorts(token, switchIds,
                        new ReturnValueCompletion<Map<String, List<PhysicalSwitchPortVO>>>(trigger) {
                            @Override
                            public void success(Map<String, List<PhysicalSwitchPortVO>> returnValue) {
                                List<PhysicalSwitchPortVO> newSwitchPortVOS = new ArrayList<>();
                                List<PhysicalSwitchPortVO> updateSwitchPortVOS = new ArrayList<>();
                                for (Map.Entry<String, List<PhysicalSwitchPortVO>> entry: returnValue.entrySet()) {
                                    for (PhysicalSwitchPortVO vo : entry.getValue()) {
                                        vo.setAccountUuid(msg.getSession().getAccountUuid());
                                        vo.setSdnControllerUuid(self.getUuid());
                                        String switchName = switchIdToNameMap.get(vo.getSwitchUuid());
                                        if (switchName != null) {
                                            String lldpUuid = Q.New(HostNetworkInterfaceLldpRefVO.class).select(HostNetworkInterfaceLldpRefVO_.lldpUuid)
                                                    .eq(HostNetworkInterfaceLldpRefVO_.systemName, switchName)
                                                    .eq(HostNetworkInterfaceLldpRefVO_.portId, vo.getName())
                                                    .findValue();
                                            if (lldpUuid != null) {
                                                String interfaceUuid = Q.New(HostNetworkInterfaceLldpVO.class).select(HostNetworkInterfaceLldpVO_.interfaceUuid)
                                                        .eq(HostNetworkInterfaceLldpVO_.uuid, lldpUuid).findValue();
                                                logger.debug(String.format("link switch port[switchName:%s, portName:%s] to interface[%s]",
                                                        switchName, vo.getName(), interfaceUuid));
                                                vo.setPeerInterfaceUuid(interfaceUuid);
                                            }
                                        }

                                        PhysicalSwitchPortVO oldPort = dbf.findByUuid(vo.getUuid(), PhysicalSwitchPortVO.class);
                                        if (oldPort == null) {
                                            newSwitchPortVOS.add(vo);
                                        } else {
                                            oldPort.copyFromAnother(vo);
                                            updateSwitchPortVOS.add(oldPort);
                                        }
                                    }
                                }

                                if (!newSwitchPortVOS.isEmpty()) {
                                    dbf.persistCollection(newSwitchPortVOS);
                                }
                                if (!updateSwitchPortVOS.isEmpty()) {
                                    dbf.updateCollection(updateSwitchPortVOS);
                                }

                                data.put("switchPortVOS", newSwitchPortVOS);

                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                List<PhysicalSwitchPortVO> switchPortVOS = (List<PhysicalSwitchPortVO>) data.get("switchPortVOS");
                if (switchPortVOS != null && !switchPortVOS.isEmpty()) {
                    SQL.New(PhysicalSwitchPortVO.class)
                            .in(PhysicalSwitchPortVO_.uuid, switchPortVOS.stream().map(PhysicalSwitchPortVO::getUuid).collect(Collectors.toList()))
                            .delete();
                }
            }
        }).then(new Flow() {
            String __name__ = "get_huawei_imaster_vpc";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String token = (String) data.get("token");
                getHuaweiIMasterControllerVpc(token,
                        new ReturnValueCompletion<List<HuaweiIMasterVpcVO>>(trigger) {
                            @Override
                            public void success(List<HuaweiIMasterVpcVO> returnValue) {
                                List<HuaweiIMasterVpcVO> vpcs = new ArrayList<>();
                                for (HuaweiIMasterVpcVO vo: returnValue) {
                                    vo.setAccountUuid(msg.getSession().getAccountUuid());
                                    vo.setSdnControllerUuid(self.getUuid());
                                    vpcs.add(vo);
                                }

                                if (!vpcs.isEmpty()) {
                                    dbf.persistCollection(vpcs);
                                }
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                SQL.New(HuaweiIMasterVpcVO.class)
                        .eq(HuaweiIMasterVpcVO_.sdnControllerUuid, self.getUuid()).delete();
                trigger.rollback();
            }
        }).then(new NoRollbackFlow() {
            String __name__ = "delete_huawei_imaster_token";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String token = (String) data.get("token");
                deleteHuaweiIMasterToken(token, new Completion(trigger) {
                    @Override
                    public void success() {
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        //ignore delete toke error
                        trigger.next();
                    }
                });
            }
        }).done(new FlowDoneHandler(completion) {
            @Override
            public void handle(Map data) {
                completion.success();
            }
        }).error(new FlowErrorHandler(completion) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                completion.fail(errCode);
            }
        }).start();
    }

    @Override
    @SdnControllerLog
    public void postInitSdnController(SdnControllerVO vo, Completion completion) {
        completion.success();
    }

    @Override
    @SdnControllerLog
    public void preCreateVxlanNetwork(L2VxlanNetworkInventory vxlan, List<String> systemTags, Completion completion) {
        completion.success();
    }

    @Override
    @SdnControllerLog
    public void createL2Network(L2NetworkInventory inv, List<String> systemTags, Completion completion) {
        List<Flow> flows = new ArrayList<>();
        flows.add(new Flow() {
            String __name__ = "create-logical-switch";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String token = (String) data.get("token");
                String vpcId = null;
                String tenantId = null;
                for (String tag : systemTags) {
                    if (HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_VPC_UUID.isMatch(tag)) {
                        vpcId = HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_VPC_UUID.getTokenByTag(
                                tag, HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_VPC_UUID_TOKEN);
                    }

                    if (HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_TENANT_UUID.isMatch(tag)) {
                        tenantId = HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_TENANT_UUID.getTokenByTag(
                                tag, HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_TENANT_UUID_TOKEN);
                    }
                }

                createHuaweiIMasterControllerLogicalSwitch(token, inv, tenantId, vpcId, new Completion(trigger) {
                    @Override
                    public void success() {
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                String token = (String) data.get("token");
                deleteHuaweiIMasterControllerLogicalSwitch(token, inv, new Completion(trigger) {
                    @Override
                    public void success() {
                        trigger.rollback();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.rollback();
                    }
                });
            }
        });

        flows.add(new NoRollbackFlow() {
            String __name__ = "update-logical-switch-vni";
            @Override
            public void run(FlowTrigger trigger, Map data) {
                String token = (String) data.get("token");
                getHuaweiIMasterControllerLogicalSwitch(token, inv.getUuid(), new ReturnValueCompletion<List<HuaweiLogicalSwitchStruct>>(trigger) {
                    @Override
                    public void success(List<HuaweiLogicalSwitchStruct> returnValue) {
                        if (returnValue == null) {
                            if (CoreGlobalProperty.UNIT_TEST_ON) {
                                trigger.next();
                                return;
                            }

                            trigger.fail(operr("failed to get logical switch for l2 network"));
                            return;
                        }

                        if (returnValue.size() != 1) {
                            trigger.fail(operr("get logical switch for l2 network failed with result: %s",
                                    JSONObjectUtil.toJsonString(returnValue)));
                            return;
                        }

                        if (!CoreGlobalProperty.UNIT_TEST_ON) {
                            HuaweiLogicalSwitchStruct struct = returnValue.get(0);
                            HardwareL2VxlanNetworkVO vo = dbf.findByUuid(inv.getUuid(), HardwareL2VxlanNetworkVO.class);
                            vo.setVni(struct.vni);
                            dbf.update(vo);
                        }
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }
        });

        FlowChain chain = getHuaweiIMasterFlowChain(
                String.format("create-huawei-imaster-logical-switch"), flows, new Completion(completion) {
                    @Override
                    public void success() {
                        completion.success();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        completion.fail(errorCode);
                    }
                });

        chain.start();
    }

    @Override
    @SdnControllerLog
    public void deleteL2Network(L2NetworkInventory vxlan, Completion completion) {
        List<Flow> flows = new ArrayList<>();
        flows.add(new NoRollbackFlow() {
            String __name__ = "delete-logical-switch";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String token = (String) data.get("token");
                deleteHuaweiIMasterControllerLogicalSwitch(token, vxlan, new Completion(trigger) {
                    @Override
                    public void success() {
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }
        });

        FlowChain chain = getHuaweiIMasterFlowChain(
                String.format("delete-logical-switch"), flows, new Completion(completion) {
                    @Override
                    public void success() {
                        completion.success();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        completion.fail(errorCode);
                    }
                });

        chain.start();
    }

    @Override
    @SdnControllerLog
    public void postCreateVxlanNetwork(L2VxlanNetworkInventory vxlan, List<String> systemTags, Completion completion) {
        completion.success();
    }

    @Override
    @SdnControllerLog
    public void preAttachL2NetworkToCluster(L2VxlanNetworkInventory vxlan, List<String> systemTags, Completion completion) {
        completion.success();
    }

    @Override
    @SdnControllerLog
    public void attachL2NetworkToCluster(L2VxlanNetworkInventory vxlan, List<String> clusterUuids, List<String> systemTags, Completion completion) {

        List<Flow> flows = new ArrayList<>();
        flows.add(new NoRollbackFlow() {
            String __name__ = "attach-logical-switch-to-cluster";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String token = (String) data.get("token");
                createHuaweiIMasterLogicalSwitchPortForClusters(token, vxlan, clusterUuids, new Completion(trigger) {
                    @Override
                    public void success() {
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }
        });

        FlowChain chain = getHuaweiIMasterFlowChain(String.format("attach-logical-switch-to-cluster"),
                flows, new Completion(completion) {
                    @Override
                    public void success() {
                        completion.success();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        completion.fail(errorCode);
                    }
                });

        chain.start();
    }

    @Override
    public void attachL2NetworkToHosts(L2VxlanNetworkInventory vxlan, List<HostInventory> hinvs, List<String> systemTags, Completion completion) {
        List<Flow> flows = new ArrayList<>();
        flows.add(new NoRollbackFlow() {
            String __name__ = "attach-logical-switch-to-hosts";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String token = (String) data.get("token");
                createHuaweiIMasterLogicalSwitchPortForHosts(token, vxlan, hinvs, new Completion(trigger) {
                    @Override
                    public void success() {
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }
        });

        FlowChain chain = getHuaweiIMasterFlowChain(String.format("attach-logical-switch-to-hosts"),
                flows, new Completion(completion) {
                    @Override
                    public void success() {
                        completion.success();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        completion.fail(errorCode);
                    }
                });

        chain.start();
    }

    @Override
    @SdnControllerLog
    public void detachL2NetworkFromCluster(L2VxlanNetworkInventory vxlan, String clusterUuid, Completion completion) {
        List<Flow> flows = new ArrayList<>();

        flows.add(new NoRollbackFlow() {
            String __name__ = "get-logical-switch";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String token = (String) data.get("token");
                getHuaweiIMasterControllerLogicalSwitchPort(token, vxlan,
                        new ReturnValueCompletion<List<HuaweiLogicalSwitchPortStruct>>(trigger) {
                    @Override
                    public void success(List<HuaweiLogicalSwitchPortStruct> returnValue) {
                        data.put("logical_port", returnValue);
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }
        });

        flows.add(new NoRollbackFlow() {
            String __name__ = "detach-logical-switch";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String token = (String) data.get("token");
                List<HuaweiLogicalSwitchPortStruct> structs = (List<HuaweiLogicalSwitchPortStruct>) data.get("logical_port");
                if (structs == null || structs.isEmpty()) {
                    logger.debug("there is no logical switch port on sdn controller");
                    trigger.next();
                    return;
                }

                new While<>(structs).each((s, wcomp) -> {
                    if (!s.additional.isCreatedByZStack()) {
                        wcomp.done();
                        return;
                    }

                    deleteHuaweiIMasterControllerLogicalSwitchPort(token, s.id, new Completion(trigger) {
                        @Override
                        public void success() {
                            wcomp.done();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            logger.debug(String.format("delete logical switch port[%s] failed because: %s",
                                    JSONObjectUtil.toJsonString(s), errorCode.getDescription()));
                            wcomp.done();
                        }
                    });
                }).run(new WhileDoneCompletion(trigger) {
                    @Override
                    public void done(ErrorCodeList errorCodeList) {
                        trigger.next();
                    }
                });
            }
        });

        FlowChain chain = getHuaweiIMasterFlowChain(
                String.format("detach-logical-switch"), flows, new Completion(completion) {
                    @Override
                    public void success() {
                        completion.success();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        completion.fail(errorCode);
                    }
                });

        chain.start();
    }

    @Override
    @SdnControllerLog
    public void postAttachL2NetworkToCluster(L2VxlanNetworkInventory vxlan, List<String> systemTags, Completion completion) {
        completion.success();
    }

    private void deleteSdnControllerDbResource(String controllerUuid) {
        SQL.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.sdnControllerUuid, controllerUuid).delete();
        SQL.New(PhysicalSwitchVO.class).eq(PhysicalSwitchVO_.sdnControllerUuid, controllerUuid).delete();
    }

    @Override
    @SdnControllerLog
    public void deleteSdnController(SdnControllerDeletionMsg msg, SdnControllerInventory sdn, Completion completion) {
        deleteSdnControllerDbResource(msg.getSdnControllerUuid());
        completion.success();
    }

    @Override
    public List<SdnVniRange> getVniRange(SdnControllerInventory controller) {
        List<SdnVniRange> vniRanges = new ArrayList<>();
        List<Map<String, String>> tokens = SdnControllerSystemTags.VNI_RANGE.getTokensOfTagsByResourceUuid(
                controller.getUuid());
        for (Map<String, String> token : tokens) {
            SdnVniRange range = new SdnVniRange();
            for (Map.Entry<String, String> entry : token.entrySet()) {
                if (entry.getKey().equals(SdnControllerSystemTags.START_VNI_TOKEN)) {
                    range.startVni = Integer.parseInt(entry.getValue());
                }

                if (entry.getKey().equals(SdnControllerSystemTags.END_VNI_TOKEN)) {
                    range.endVni = Integer.parseInt(entry.getValue());
                }
            }
            vniRanges.add(range);
        }

        return vniRanges;
    }

    @Override
    public List<SdnVlanRange> getVlanRange(SdnControllerInventory controller) {
        List<SdnVlanRange> vlanRanges = new ArrayList<>();
        List<Map<String, String>> tokens = SdnControllerSystemTags.VLAN_RANGE.getTokensOfTagsByResourceUuid(
                controller.getUuid());
        for (Map<String, String> token : tokens) {
            SdnVlanRange range = new SdnVlanRange();
            for (Map.Entry<String, String> entry : token.entrySet()) {
                if (entry.getKey() == SdnControllerSystemTags.START_VLAN_TOKEN) {
                    range.startVlan = Integer.parseInt(entry.getValue());
                }

                if (entry.getKey() == SdnControllerSystemTags.END_VLAN_TOKEN) {
                    range.endVlan = Integer.parseInt(entry.getValue());
                }
            }
            vlanRanges.add(range);
        }

        return vlanRanges;
    }


    @Override
    public void createL3Network(L3NetworkInventory inv, List<String> systemTags, Completion completion) {
        String lRouterUuid = HuaweiIMasterHelper.getL3NetworkLogicalRouterId(systemTags);
        String logicalNetworkUuid = HuaweiIMasterHelper.getL2NetworkVpcId(inv.getL2NetworkUuid());

        List<Flow> flows = new ArrayList<>();
        flows.add(new NoRollbackFlow() {
            String __name__ = "create-logical-link";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String token = (String) data.get("token");
                createHuaweiIMasterLogicalLink(token, lRouterUuid, logicalNetworkUuid, inv.getUuid(),
                        inv.getL2NetworkUuid(), new Completion(trigger) {
                    @Override
                    public void success() {
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }
        });

        FlowChain chain = getHuaweiIMasterFlowChain(
                String.format("create-logical-link"), flows, new Completion(completion) {
                    @Override
                    public void success() {
                        completion.success();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        completion.fail(errorCode);
                    }
                });

        chain.start();
    }

    @Override
    public void deleteL3Network(L3NetworkInventory inv, Completion completion) {
        List<Flow> flows = new ArrayList<>();
        flows.add(new NoRollbackFlow() {
            String __name__ = "delete-logical-link";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String token = (String) data.get("token");
                deleteHuaweiIMasterLogicalLink(token, inv.getUuid(), new Completion(trigger) {
                            @Override
                            public void success() {
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
            }
        });

        FlowChain chain = getHuaweiIMasterFlowChain(
                String.format("delete-logical-link"), flows, new Completion(completion) {
                    @Override
                    public void success() {
                        completion.success();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        completion.fail(errorCode);
                    }
                });

        chain.start();
    }

    @Override
    public void createIpRange(IpRangeInventory inv, Completion completion) {
        List<Flow> flows = new ArrayList<>();
        flows.add(new Flow() {
            String __name__ = "create-subnet";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String token = (String) data.get("token");
                createHuaweiIMasterSubnet(token, inv, new Completion(trigger) {
                    @Override
                    public void success() {
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                String token = (String) data.get("token");
                deleteHuaweiIMasterSubnet(token, inv, new Completion(trigger) {
                    @Override
                    public void success() {
                        trigger.rollback();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.rollback();
                    }
                });
            }
        });

        flows.add(new NoRollbackFlow() {
            String __name__ = "update-logical-switch-subnet";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String token = (String) data.get("token");
                L3NetworkVO l3vo = dbf.findByUuid(inv.getL3NetworkUuid(), L3NetworkVO.class);

                getHuaweiIMasterControllerLogicalSwitch(token, l3vo.getL2NetworkUuid(), new ReturnValueCompletion<List<HuaweiLogicalSwitchStruct>>(trigger) {
                    @Override
                    public void success(List<HuaweiLogicalSwitchStruct> returnValue) {
                        if (returnValue == null || returnValue.isEmpty()) {
                            trigger.fail(operr("get logical switch failed: uuid: %s", l3vo.getL2NetworkUuid()));
                            return;
                        }

                        HuaweiLogicalSwitchStruct logicalSwitch = new HuaweiLogicalSwitchStruct();
                        logicalSwitch.id = returnValue.get(0).id;
                        logicalSwitch.logicNetworkId = returnValue.get(0).logicNetworkId;
                        logicalSwitch.name = returnValue.get(0).name;
                        logicalSwitch.subnets = new ArrayList<>();
                        if (returnValue.get(0).subnets != null) {
                            for (String subnet : returnValue.get(0).subnets) {
                                if (!StringUtils.isEmpty(subnet)) {
                                    logicalSwitch.subnets.add(subnet);
                                }
                            }
                        }
                        String huaweiCidr = String.format("%s/%s", inv.getGateway(),
                                NetworkUtils.getPrefixLengthFromNetmask(inv.getNetmask()));
                        if (logicalSwitch.subnets.contains(huaweiCidr)) {
                            logger.debug(String.format("logical switch[%s] already contains subnet[%s]", logicalSwitch.name, inv.getNetworkCidr()));
                            trigger.next();
                            return;
                        }
                        logicalSwitch.subnets.add(huaweiCidr);
                        updateHuaweiIMasterControllerLogicalSwitch(token, logicalSwitch, new Completion(trigger) {
                            @Override
                            public void success() {
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }
        });

        FlowChain chain = getHuaweiIMasterFlowChain(
                String.format("create-subnet"), flows, new Completion(completion) {
                    @Override
                    public void success() {
                        completion.success();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        completion.fail(errorCode);
                    }
                });

        chain.start();
    }

    @Override
    public void deleteIpRange(IpRangeInventory inv, Completion completion) {
        List<Flow> flows = new ArrayList<>();
        flows.add(new NoRollbackFlow() {
            String __name__ = "delete-subnet";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String token = (String) data.get("token");
                deleteHuaweiIMasterSubnet(token, inv, new Completion(trigger) {
                    @Override
                    public void success() {
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }
        });

        FlowChain chain = getHuaweiIMasterFlowChain(
                String.format("delete-subnet"), flows, new Completion(completion) {
                    @Override
                    public void success() {
                        completion.success();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        completion.fail(errorCode);
                    }
                });

        chain.start();
    }

    @Override
    public void createVRouter(Completion completion) {

    }

    public void getHuaweiIMasterToken(ReturnValueCompletion<String> completion) {
        HuaweiIMasterNceFabricCommands.GetTokenCmd cmd = new HuaweiIMasterNceFabricCommands.GetTokenCmd();
        HuaweiIMasterNceFabricCommands.LoginCmd loginCmd = new HuaweiIMasterNceFabricCommands.LoginCmd();
        cmd.userName = self.getUsername();
        cmd.password = self.getPassword();
        //cmd.login = loginCmd;

        try {
            HuaweiIMasterNceFabricCommands.LoginRsp rsp = new HuaweiIMasterNceFabricHttpClient<>(HuaweiIMasterNceFabricCommands.LoginRsp.class)
                    .syncCall(HttpMethod.POST, self.getIp(), HuaweiIMasterNceFabricCommands.HUAWEI_IMASTER_TOKEN_PATH, cmd, getHeaders());
            if (rsp == null) {
                completion.fail(operr("get token of sdn controller [ip:%s] failed", self.getIp()));
                return;
            }
            completion.success(rsp.data.token_id);
        } catch (Exception e) {
            completion.fail(operr("get token of sdn controller [ip:%s] failed because %s", self.getIp(), e.getMessage()));
        }
    }

    private void getHuaweiIMasterSystemInfo(String token, ReturnValueCompletion<HuaweiIMasterNceFabricCommands.GetSystemInfoRsp> completion) {
        HuaweiIMasterNceFabricCommands.GetSystemInfoCmd cmd = new HuaweiIMasterNceFabricCommands.GetSystemInfoCmd();

        try {
            HuaweiIMasterNceFabricCommands.GetSystemInfoRsp rsp = new HuaweiIMasterNceFabricHttpClient<>(HuaweiIMasterNceFabricCommands.GetSystemInfoRsp.class)
                    .syncCall(HttpMethod.GET, self.getIp(), HuaweiIMasterNceFabricCommands.HUAWEI_IMASTER_SYSTEM_INFO_PATH, cmd, getHeaders(token));
            if (rsp == null) {
                completion.fail(operr("get system info of sdn controller [ip:%s] failed", self.getIp()));
                return;
            }
            completion.success(rsp);
        } catch (Exception e) {
            completion.fail(operr("get system info of sdn controller [ip:%s] failed because %s", self.getIp(), e.getMessage()));
        }
    }

    public void deleteHuaweiIMasterToken(String token, Completion completion) {
        HuaweiIMasterNceFabricCommands.DeleteTokenCmd cmd = new HuaweiIMasterNceFabricCommands.DeleteTokenCmd();
        cmd.token = token;

        try {
            HuaweiIMasterNceFabricCommands.LoginRsp rsp = new HuaweiIMasterNceFabricHttpClient<>(HuaweiIMasterNceFabricCommands.LoginRsp.class)
                    .syncCall(HttpMethod.DELETE, self.getIp(), HuaweiIMasterNceFabricCommands.HUAWEI_IMASTER_TOKEN_PATH, cmd, getHeaders());

            completion.success();
        } catch (Exception e) {
            completion.fail(operr("delete token of sdn controller [ip:%s] failed because %s", self.getIp(), e.getMessage()));
        }
    }

    private void getHuaweiIMasterControllerTenant(String token, ReturnValueCompletion<List<HuaweiIMasterNceFabricCommands.TenantStruct>> completion) {
        HuaweiIMasterNceFabricCommands.GetTenantCmd cmd = new HuaweiIMasterNceFabricCommands.GetTenantCmd();

        try {
            HuaweiIMasterNceFabricCommands.GetTenantRsp rsp = new HuaweiIMasterNceFabricHttpClient<>(HuaweiIMasterNceFabricCommands.GetTenantRsp.class)
                    .syncCall(HttpMethod.GET, self.getIp(), HuaweiIMasterNceFabricCommands.HUAWEI_IMASTER_TENANT_PATH, cmd, getHeaders(token));
            if (rsp == null) {
                completion.fail(operr("get tenant of sdn controller [ip:%s] failed", self.getIp()));
                return;
            }

            completion.success(rsp.tenant);
        } catch (Exception e) {
            completion.fail(operr("get tenant of sdn controller [ip:%s] failed because %s", self.getIp(), e.getMessage()));
        }
    }

    private void getHuaweiIMasterControllerSwitches(String token, ReturnValueCompletion<List<HuaweiIMasterNceFabricCommands.SwitchStruct>> completion) {
        HuaweiIMasterNceFabricCommands.GetSwitchCmd cmd = new HuaweiIMasterNceFabricCommands.GetSwitchCmd();

        try {
            HuaweiIMasterNceFabricCommands.GetSwitchRsp rsp = new HuaweiIMasterNceFabricHttpClient<>(HuaweiIMasterNceFabricCommands.GetSwitchRsp.class)
                    .syncCall(HttpMethod.GET, self.getIp(), HuaweiIMasterNceFabricCommands.HUAWEI_IMASTER_SWITCH_PATH, cmd, getHeaders(token));
            if (rsp == null) {
                completion.fail(operr("get switch device of sdn controller [ip:%s] failed", self.getIp()));
                return;
            }

            completion.success(rsp.devices);
        } catch (Exception e) {
            completion.fail(operr("get switch device of sdn controller [ip:%s] failed because %s", self.getIp(), e.getMessage()));
        }
    }

    private void getHuaweiIMasterControllerSwitchPorts(String token, List<String> switchUuids,
                     ReturnValueCompletion<Map<String, List<PhysicalSwitchPortVO>>> completion) {
        HuaweiIMasterNceFabricCommands.GetSwitchPortCmd cmd = new HuaweiIMasterNceFabricCommands.GetSwitchPortCmd();
        cmd.deviceIdList = switchUuids;

        try {
            HuaweiIMasterNceFabricCommands.GetSwitchPortRsp rsp = new HuaweiIMasterNceFabricHttpClient<>
                    (HuaweiIMasterNceFabricCommands.GetSwitchPortRsp.class)
                    .syncCall(HttpMethod.POST, self.getIp(), HuaweiIMasterNceFabricCommands.HUAWEI_IMASTER_SWITCH_PORT_PATH,
                            cmd, getHeaders(token));
            if (rsp == null) {
                completion.fail(operr("get logical switch port of sdn controller [ip:%s] failed", self.getIp()));
                return;
            }

            Map<String, List<PhysicalSwitchPortVO>> portMap = rsp.getDevicePortMap();
            completion.success(portMap);
        } catch (Exception e) {
            completion.fail(operr("get switch port of sdn controller [ip:%s] failed because %s", self.getIp(), e.getMessage()));
        }
    }

    private void getHuaweiIMasterControllerVpc(String token,
                      ReturnValueCompletion<List<HuaweiIMasterVpcVO>> completion) {
        HuaweiIMasterNceFabricCommands.GetVpcCmd cmd = new HuaweiIMasterNceFabricCommands.GetVpcCmd();

        try {
            HuaweiIMasterNceFabricCommands.GetVpcRsp rsp = new HuaweiIMasterNceFabricHttpClient<>
                    (HuaweiIMasterNceFabricCommands.GetVpcRsp.class)
                    .syncCall(HttpMethod.GET, self.getIp(), HuaweiIMasterNceFabricCommands.HUAWEI_IMASTER_VPC_PATH,
                            cmd, getHeaders(token));
            if (rsp == null) {
                completion.fail(operr("get vpc of sdn controller [ip:%s] failed", self.getIp()));
                return;
            }

            List<HuaweiIMasterVpcVO> vpcs = new ArrayList<>();
            for (HuaweiIMasterNceFabricCommands.VpcStruct vpc : rsp.network) {
                HuaweiIMasterVpcVO vo = vpc.toHuaweiIMasterLogicalNetworkVO();
                vpcs.add(vo);
            }
            completion.success(vpcs);
        } catch (Exception e) {
            completion.fail(operr("get vpc of sdn controller [ip:%s] failed because %s", self.getIp(), e.getMessage()));
        }
    }

    private void getHuaweiIMasterControllerFabric(String token,
                                               ReturnValueCompletion<List<HuaweiIMasterFabricVO>> completion) {
        HuaweiIMasterNceFabricCommands.GetFabricCmd cmd = new HuaweiIMasterNceFabricCommands.GetFabricCmd();

        try {
            HuaweiIMasterNceFabricCommands.GetFabricRsp rsp = new HuaweiIMasterNceFabricHttpClient<>
                    (HuaweiIMasterNceFabricCommands.GetFabricRsp.class)
                    .syncCall(HttpMethod.GET, self.getIp(), HuaweiIMasterNceFabricCommands.HUAWEI_IMASTER_FABRIC_PATH,
                            cmd, getHeaders(token));
            if (rsp == null) {
                completion.fail(operr("get fabric of sdn controller [ip:%s] failed", self.getIp()));
                return;
            }

            List<HuaweiIMasterFabricVO> fabrics = new ArrayList<>();
            for (HuaweiIMasterNceFabricCommands.FabricStruct fabric : rsp.fabric) {
                HuaweiIMasterFabricVO vo = fabric.toHuaweiIMasterFabricVO();
                fabrics.add(vo);
            }
            completion.success(fabrics);
        } catch (Exception e) {
            completion.fail(operr("get fabric of sdn controller [ip:%s] failed because %s", self.getIp(), e.getMessage()));
        }
    }

    private void createHuaweiIMasterControllerLogicalSwitch(String token, L2NetworkInventory inv, String tenantId,
                                                            String vpcId, Completion completion) {
        if (vpcId == null) {
            vpcId = HuaweiIMasterHelper.getL2NetworkVpcId(inv.getUuid());
        }
        if (tenantId == null) {
            tenantId = HuaweiIMasterHelper.getL2NetworkTenantId(inv.getUuid());
        }
        HardwareL2VxlanNetworkVO vxlanVO = dbf.findByUuid(inv.getUuid(), HardwareL2VxlanNetworkVO.class);
        CreateHuaweiLogicalSwitchCmd cmd = new CreateHuaweiLogicalSwitchCmd();
        HuaweiLogicalSwitchStruct struct = new HuaweiLogicalSwitchStruct();
        struct.id = HuaweiIMasterHelper.zStackUuidToHuaweiIMasterId(inv.getUuid());
        struct.name = inv.getName();
        struct.description = HuaweiIMasterHelper.getDescription(inv.getUuid());
        struct.logicNetworkId = HuaweiIMasterHelper.zStackUuidToHuaweiIMasterId(vpcId);
        struct.mappingType = HuaweiIMasterLogicalSwitchMappingType.NONE.toString();
        struct.macAddress = MacOperator.generateMacWithDeviceId((short)0);
        //struct.vlan = new ArrayList<>();
        //struct.vlan.add(vxlanVO.getVlan());
        struct.tenantId = HuaweiIMasterHelper.zStackUuidToHuaweiIMasterId(tenantId);
        struct.additional = new HuaweiSdnAdditional();

        cmd.logicalSwitch = new ArrayList<>();
        cmd.logicalSwitch.add(struct);

        try {
            CreateHuaweiLogicalSwitchRsp rsp = new HuaweiIMasterNceFabricHttpClient<>
                    (CreateHuaweiLogicalSwitchRsp.class)
                    .syncCall(HttpMethod.POST, self.getIp(), HUAWEI_IMASTER_LOGICAL_SWITCHES_PATH,
                            cmd, getHeaders(token));

            logger.debug(String.format("create huawei imaster logical switch [%s] successfully",
                    JSONObjectUtil.toJsonString(struct)));
            completion.success();
        } catch (Exception e) {
            completion.fail(operr("create huawei imaster logical switch [%s] failed because %s",
                    JSONObjectUtil.toJsonString(struct), e.getMessage()));
        }
    }

    private void deleteHuaweiIMasterControllerLogicalSwitch(String token, L2NetworkInventory inv, Completion completion) {
        DeleteHuaweiLogicalSwitchCmd cmd = new DeleteHuaweiLogicalSwitchCmd();

        String delPath = HuaweiIMasterNceFabricCommands.getHuaweiIMasterResourcePath(HUAWEI_IMASTER_LOGICAL_SWITCH_PATH,
                HuaweiIMasterHelper.zStackUuidToHuaweiIMasterId(inv.getUuid()));
        try {
            new HuaweiIMasterNceFabricHttpClient<>(DeleteHuaweiLogicalSwitchRsp.class)
                    .syncCall(HttpMethod.DELETE, self.getIp(), delPath, cmd, getHeaders(token));

            logger.debug(String.format("delete huawei imaster logical switch [%s] successfully", delPath));
            completion.success();
        } catch (Exception e) {
            completion.fail(operr("delete huawei imaster logical switch [%s] failed because %s", delPath, e.getMessage()));
        }
    }

    private void getHuaweiIMasterControllerLogicalSwitch(String token, String l2Uuid,
                                                         ReturnValueCompletion<List<HuaweiLogicalSwitchStruct>> completion) {
        GetHuaweiLogicalSwitchCmd cmd = new GetHuaweiLogicalSwitchCmd();

        String path = HuaweiIMasterNceFabricCommands.getHuaweiIMasterResourcePath(HUAWEI_IMASTER_LOGICAL_SWITCH_PATH,
                HuaweiIMasterHelper.zStackUuidToHuaweiIMasterId(l2Uuid));
        try {
            HuaweiIMasterNceFabricCommands.GetHuaweiLogicalSwitchRsp rsp =
                    new HuaweiIMasterNceFabricHttpClient<>(GetHuaweiLogicalSwitchRsp.class)
                    .syncCall(HttpMethod.GET, self.getIp(), path, cmd, getHeaders(token));
            if (rsp == null) {
                completion.fail(operr("get imaster logical switch [%s] no rsp", path));
                return;
            }

            logger.debug(String.format("get huawei imaster logical switch [%s] successfully", path));
            completion.success(rsp.logicalSwitch);
        } catch (Exception e) {
            completion.fail(operr("delete huawei imaster logical switch [%s] failed because %s", path, e.getMessage()));
        }
    }

    private void updateHuaweiIMasterControllerLogicalSwitch(String token, HuaweiLogicalSwitchStruct struct, Completion completion) {
        UpdateHuaweiLogicalSwitchCmd cmd = new UpdateHuaweiLogicalSwitchCmd();
        cmd.logicalSwitch = new ArrayList<>();
        cmd.logicalSwitch.add(struct);

        String path = HuaweiIMasterNceFabricCommands.getHuaweiIMasterResourcePath(HUAWEI_IMASTER_LOGICAL_SWITCH_PATH, struct.id);
        try {
            new HuaweiIMasterNceFabricHttpClient<>(UpdateHuaweiLogicalSwitchRsp.class)
                            .syncCall(HttpMethod.PUT, self.getIp(), path, cmd, getHeaders(token));

            logger.debug(String.format("update huawei imaster logical switch [%s] successfully", path));
            completion.success();
        } catch (Exception e) {
            completion.fail(operr("update huawei imaster logical switch [%s] failed because %s", path, e.getMessage()));
        }
    }

    private void createHuaweiIMasterLogicalSwitchPortForClusters(String token, L2NetworkInventory l2Inv,
                                                                 List<String> clusterUuids, Completion completion) {
        HardwareL2VxlanNetworkInventory inv = HardwareL2VxlanNetworkInventory.valueOf(
                dbf.findByUuid(l2Inv.getUuid(), HardwareL2VxlanNetworkVO.class));
        CreateHuaweiLogicalSwitchPortCmd cmd = new CreateHuaweiLogicalSwitchPortCmd();
        cmd.port = new ArrayList<>();

        Map<String, List<HuaweiLogicalSwitchPortLocationStruct>> locations;
        try {
            locations = helper.getPhysicalSwitchPortForClusters(inv, clusterUuids);
        } catch (CloudRuntimeException e) {
            completion.fail(operr("can not get physical switch port because: %s", e.getMessage()));
            return;
        }

        if (locations.isEmpty()) {
            logger.debug("there is no physical switch port linked");
            completion.success();
            return;
        }

        List<HostVO> hostVOS = Q.New(HostVO.class).in(HostVO_.clusterUuid, clusterUuids).list();
        createHuaweiIMasterLogicalSwitchPortForHosts(token, l2Inv, HostInventory.valueOf(hostVOS), completion);
    }

    private void createHuaweiIMasterLogicalSwitchPortForHosts(String token, L2NetworkInventory l2Inv,
                                                                 List<HostInventory> hinvs, Completion completion) {
        HardwareL2VxlanNetworkInventory inv = HardwareL2VxlanNetworkInventory.valueOf(
                dbf.findByUuid(l2Inv.getUuid(), HardwareL2VxlanNetworkVO.class));
        CreateHuaweiLogicalSwitchPortCmd cmd = new CreateHuaweiLogicalSwitchPortCmd();
        cmd.port = new ArrayList<>();

        List<String> hostUuids = hinvs.stream().map(HostInventory::getUuid).collect(Collectors.toList());
        Map<String, List<HuaweiLogicalSwitchPortLocationStruct>> locations;
        try {
            locations = helper.getPhysicalSwitchPortForHosts(inv, hostUuids, false);
        } catch (CloudRuntimeException e) {
            completion.fail(operr("can not get physical switch port because: %s", e.getMessage()));
            return;
        }

        if (locations.isEmpty()) {
            logger.debug("there is no physical switch port linked");
            completion.success();
            return;
        }

        List<HuaweiLogicalSwitchPortLocationStruct> newLocations = locations.values().stream().flatMap(List::stream).collect(Collectors.toList());
        getHuaweiIMasterControllerLogicalSwitchPort(token, inv, new ReturnValueCompletion<List<HuaweiLogicalSwitchPortStruct>>(completion) {
            @Override
            public void success(List<HuaweiLogicalSwitchPortStruct> returnValue) {
                List<HuaweiLogicalSwitchPortLocationStruct> curLocations = new ArrayList<>();
                if (returnValue != null) {
                    for (HuaweiLogicalSwitchPortStruct struct : returnValue) {
                        for (HuaweiLogicalSwitchPortLocationStruct loc : struct.accessInfo.location) {
                            // ignore all fields except: deviceId, portName
                            loc.deviceGroupId = null;
                            loc.deviceGroupName = null;
                            loc.deviceName = null;
                            loc.portId = null;
                            loc.deviceIp = null;
                            curLocations.add(loc);
                        }
                    }
                }

                HttpMethod medhod = HttpMethod.POST;
                if (!curLocations.isEmpty()){
                    medhod = HttpMethod.PUT;
                }

                boolean added = false;
                for (HuaweiLogicalSwitchPortLocationStruct newloc : newLocations) {
                    boolean found = false;
                    for (HuaweiLogicalSwitchPortLocationStruct curloc : curLocations) {
                        if (newloc.equals(curloc)) {
                            found = true;
                            break;
                        }
                    }

                    if (!found) {
                        added = true;
                        curLocations.add(newloc);
                    }
                }

                if (!added) {
                    completion.success();
                    return;
                }

                HardwareVxlanHelper.VxlanHostMappingStruct vstruct = HardwareVxlanHelper.getHardwareVxlanMappingVxlanId(inv, hinvs.get(0));
                HuaweiLogicalSwitchPortStruct struct = new HuaweiLogicalSwitchPortStruct();
                struct.id = HuaweiIMasterHelper.zStackUuidToHuaweiIMasterId(inv.getUuid());
                struct.name = inv.getName();
                struct.logicSwitchId = HuaweiIMasterHelper.zStackUuidToHuaweiIMasterId(inv.getUuid());
                struct.tenantId = HuaweiIMasterHelper.getL2NetworkTenantId(inv.getUuid());
                struct.fabricId = HuaweiIMasterHelper.getL2NetworkFabricId(inv.getUuid());
                struct.additional = new HuaweiSdnAdditional();

                struct.accessInfo = new HuaweiLogicalSwitchPortAccessInfoStruct();
                struct.accessInfo.mode = HuaweiIMasterAccessMode.Uni.toString();
                struct.accessInfo.type = HuaweiIMasterVlanMode.Dot1q.toString();
                struct.accessInfo.vlan = vstruct.getVlanId();
                struct.accessInfo.location = curLocations;
                cmd.port.add(struct);

                try {
                    if (medhod == HttpMethod.POST) {
                        new HuaweiIMasterNceFabricHttpClient<>(CreateHuaweiLogicalSwitchPortRsp.class)
                                .syncCall(medhod, self.getIp(), HUAWEI_IMASTER_LOGICAL_PORTS_PATH, cmd, getHeaders(token));

                        logger.debug(String.format("[%s] huawei imaster logical switch port [%s] successfully",
                                medhod, JSONObjectUtil.toJsonString(cmd)));
                    } else {
                        String path = HuaweiIMasterNceFabricCommands.getHuaweiIMasterResourcePath(HUAWEI_IMASTER_LOGICAL_PORT_PATH,
                                struct.id);
                        new HuaweiIMasterNceFabricHttpClient<>(CreateHuaweiLogicalSwitchPortRsp.class)
                                .syncCall(medhod, self.getIp(), path, cmd, getHeaders(token));

                        logger.debug(String.format("[%s] huawei imaster logical switch port [%s] successfully",
                                medhod, JSONObjectUtil.toJsonString(cmd)));
                    }
                    completion.success();
                } catch (Exception e) {
                    completion.fail(operr("[%s] huawei imaster logical switch port [%s] failed because %s",
                            medhod, JSONObjectUtil.toJsonString(cmd), e.getMessage()));
                }
            }

            @Override
            public void fail(ErrorCode errorCode) {
                completion.fail(errorCode);
            }
        });
    }

    private void getHuaweiIMasterControllerLogicalSwitchPort(String token, L2NetworkInventory inv, ReturnValueCompletion<List<HuaweiLogicalSwitchPortStruct>> completion) {
        GetHuaweiLogicalSwitchPortCmd cmd = new GetHuaweiLogicalSwitchPortCmd();

        try {
            Map<String, String> params = new HashMap<>();
            params.put("logicSwitchId", HuaweiIMasterHelper.zStackUuidToHuaweiIMasterId(inv.getUuid()));

            GetHuaweiLogicalSwitchPortRsp rsp = new HuaweiIMasterNceFabricHttpClient<>
                    (GetHuaweiLogicalSwitchPortRsp.class)
                    .syncGet(self.getIp(), HUAWEI_IMASTER_LOGICAL_PORTS_PATH, cmd, getHeaders(token), params);
            if (rsp == null) {
                logger.debug(String.format("there is no logical switch port for l2 network[%s]", inv.getUuid()));
                completion.success(new ArrayList<>());
                return;
            }

            completion.success(rsp.port);
        } catch (Exception e) {
            completion.fail(operr("get logical switch port pf logical switch[%s] failed because %s", inv.getUuid(), e.getMessage()));
        }
    }

    private void deleteHuaweiIMasterControllerLogicalSwitchPort(String token, String logicalSwitchPortId, Completion completion) {
        DeleteHuaweiLogicalSwitchPortCmd cmd = new DeleteHuaweiLogicalSwitchPortCmd();

        String delPath = HuaweiIMasterNceFabricCommands.getHuaweiIMasterResourcePath(HUAWEI_IMASTER_LOGICAL_PORT_PATH,
                logicalSwitchPortId);
        try {
            DeleteHuaweiLogicalSwitchPortRsp rsp = new HuaweiIMasterNceFabricHttpClient<>
                    (DeleteHuaweiLogicalSwitchPortRsp.class)
                    .syncCall(HttpMethod.DELETE, self.getIp(), delPath, cmd, getHeaders(token));

            logger.debug(String.format("delete huawei imaster logical switch port [%s] successfully", delPath));
            completion.success();
        } catch (Exception e) {
            completion.fail(operr("delete huawei imaster logical switch port [%s] failed because %s", delPath, e.getMessage()));
        }
    }

    private void createHuaweiIMasterLogicalRouter(String token,
              HuaweiIMasterVRouterInventory inv, Completion completion) {
        CreateHuaweiLogicalRouterCmd cmd = new CreateHuaweiLogicalRouterCmd();

        GetHuaweiLogicalRouterLocationStruct locationStruct = new GetHuaweiLogicalRouterLocationStruct();
        locationStruct.fabricId = HuaweiIMasterHelper.zStackUuidToHuaweiIMasterId(inv.getFabricUuid());

        HuaweiLogicalRouterStruct struct = new HuaweiLogicalRouterStruct();
        struct.id = HuaweiIMasterHelper.zStackUuidToHuaweiIMasterId(inv.getUuid());
        struct.name = inv.getName();
        struct.description = inv.getDescription();
        struct.logicNetworkId = HuaweiIMasterHelper.zStackUuidToHuaweiIMasterId(
                inv.getLogicalNetworkId());
        struct.type = HuaweiIMasterLogicalRouterType.NORMAL.toString();
        struct.routerLocations = new ArrayList<>();
        struct.routerLocations.add(locationStruct);
        struct.tenantId = HuaweiIMasterHelper.zStackUuidToHuaweiIMasterId(inv.getTenantId());
        struct.additional = new HuaweiSdnAdditional();

        cmd.router = struct;

        try {
            new HuaweiIMasterNceFabricHttpClient<>(CreateHuaweiLogicalRouterRsp.class)
                    .syncCall(HttpMethod.POST, self.getIp(), HUAWEI_IMASTER_LOGICAL_ROUTERS_PATH, cmd, getHeaders(token));
            completion.success();
        } catch (Exception e) {
            completion.fail(operr("create huawei imaster logical router[%s] failed, because %s",
                    JSONObjectUtil.toJsonString(struct), e.getMessage()));
        }
    }

    private void deleteHuaweiIMasterLogicalRouter(String token, String vRouterUuid, Completion completion) {
        DeleteHuaweiLogicalRouterCmd cmd = new DeleteHuaweiLogicalRouterCmd();

        String delPath = HuaweiIMasterNceFabricCommands.getHuaweiIMasterResourcePath(HUAWEI_IMASTER_LOGICAL_ROUTER_PATH,
                HuaweiIMasterHelper.zStackUuidToHuaweiIMasterId(vRouterUuid));

        try {
            new HuaweiIMasterNceFabricHttpClient<>(DeleteHuaweiLogicalRouterRsp.class)
                    .syncCall(HttpMethod.DELETE, self.getIp(), delPath, cmd, getHeaders(token));
            completion.success();
        } catch (Exception e) {
            completion.fail(operr("delete huawei imaster logical router[%s] failed, because %s",
                    delPath, e.getMessage()));
        }
    }

    private void createHuaweiIMasterLogicalLink(String token, String vRouterUuid,
                                                String vpcId, String l3Uuid, String l2Uuid, Completion completion) {
        CreateHuaweiLogicalLinkCmd cmd = new CreateHuaweiLogicalLinkCmd();

        HuaweiLogicalLinkStruct linkStruct = new HuaweiLogicalLinkStruct();
        linkStruct.id = HuaweiIMasterHelper.zStackUuidToHuaweiIMasterId(l3Uuid);
        linkStruct.logicSwitchId = HuaweiIMasterHelper.zStackUuidToHuaweiIMasterId(l2Uuid);
        linkStruct.logicRouterId = vRouterUuid;
        linkStruct.logicNetworkId = vpcId;
        linkStruct.additional = new HuaweiSdnAdditional();

        cmd.link = new ArrayList<>();
        cmd.link.add(linkStruct);

        try {
            CreateHuaweiLogicalLinkRsp rsp = new HuaweiIMasterNceFabricHttpClient<>(CreateHuaweiLogicalLinkRsp.class)
                    .syncCall(HttpMethod.POST, self.getIp(), HUAWEI_IMASTER_LOGICAL_LINKS_PATH, cmd, getHeaders(token));
            completion.success();
        } catch (Exception e) {
            completion.fail(operr("create huawei imaster logical link[%s] failed, because %s",
                    JSONObjectUtil.toJsonString(linkStruct), e.getMessage()));
        }
    }

    private void deleteHuaweiIMasterLogicalLink(String token, String l3Uuid, Completion completion) {
        DeleteHuaweiLogicalLinkCmd cmd = new DeleteHuaweiLogicalLinkCmd();

        String delPath = HuaweiIMasterNceFabricCommands.getHuaweiIMasterResourcePath(HUAWEI_IMASTER_LOGICAL_LINK_PATH,
                HuaweiIMasterHelper.zStackUuidToHuaweiIMasterId(l3Uuid));

        try {
            DeleteHuaweiLogicalLinkRsp rsp = new HuaweiIMasterNceFabricHttpClient<>(DeleteHuaweiLogicalLinkRsp.class)
                    .syncCall(HttpMethod.DELETE, self.getIp(), delPath, cmd, getHeaders(token));
            completion.success();
        } catch (Exception e) {
            completion.fail(operr("delete huawei imaster logical link[%s] failed, because %s",
                    delPath, e.getMessage()));
        }
    }

    private void createHuaweiIMasterSubnet(String token, IpRangeInventory inv, Completion completion) {
        CreateHuaweiSubnetCmd cmd = new CreateHuaweiSubnetCmd();

        L3NetworkVO l3Vo = dbf.findByUuid(inv.getL3NetworkUuid(), L3NetworkVO.class);

        HuaweiSubnetStruct subnetStruct = new HuaweiSubnetStruct();
        subnetStruct.id = HuaweiIMasterHelper.zStackUuidToHuaweiIMasterId(inv.getUuid());
        subnetStruct.logicSwitchId = HuaweiIMasterHelper.zStackUuidToHuaweiIMasterId(l3Vo.getL2NetworkUuid());
        subnetStruct.logicRouterId = HuaweiIMasterHelper.getL3NetworkLogicalRouterId(inv.getL3NetworkUuid());
        subnetStruct.tenantId = HuaweiIMasterHelper.getL2NetworkTenantId(l3Vo.getL2NetworkUuid());
        subnetStruct.cidr = inv.getNetworkCidr();
        subnetStruct.gatewayIp = inv.getGateway();
        subnetStruct.dhcpEnable = false;
        if (inv.getIpVersion() == IPv6Constants.IPv6) {
            subnetStruct.ipv6RaMode = HuaweiIMasterHelper.getHuaweiIpv6AddressMode(inv.getAddressMode());
        }
        subnetStruct.additional = new HuaweiSdnAdditional();

        cmd.subnet = new ArrayList<>();
        cmd.subnet.add(subnetStruct);

        try {
            new HuaweiIMasterNceFabricHttpClient<>(CreateHuaweiSubnetRsp.class)
                    .syncCall(HttpMethod.POST, self.getIp(), HUAWEI_IMASTER_SUBNETS_PATH, cmd, getHeaders(token));
            completion.success();
        } catch (Exception e) {
            completion.fail(operr("create huawei imaster subnet[%s] failed, because %s",
                    JSONObjectUtil.toJsonString(subnetStruct), e.getMessage()));
        }
    }

    private void deleteHuaweiIMasterSubnet(String token, IpRangeInventory inv, Completion completion) {
        DeleteHuaweiSubnetCmd cmd = new DeleteHuaweiSubnetCmd();

        String delPath = HuaweiIMasterNceFabricCommands.getHuaweiIMasterResourcePath(HUAWEI_IMASTER_SUBNET_PATH,
                HuaweiIMasterHelper.zStackUuidToHuaweiIMasterId(inv.getUuid()));

        try {
            DeleteHuaweiSubnetRsp rsp = new HuaweiIMasterNceFabricHttpClient<>(DeleteHuaweiSubnetRsp.class)
                    .syncCall(HttpMethod.DELETE, self.getIp(), delPath, cmd, getHeaders(token));
            completion.success();
        } catch (Exception e) {
            completion.fail(operr("delete huawei imaster subnet[%s] failed, because %s",
                    delPath, e.getMessage()));
        }
    }


    private FlowChain getHuaweiIMasterFlowChain(String name, List<Flow> flows, Completion completion) {
        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName(name);
        chain.then(new NoRollbackFlow() {
            String __name__ = "get_huawei_imaster_token";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                getHuaweiIMasterToken(new ReturnValueCompletion<String>(trigger) {
                    @Override
                    public void success(String returnValue) {
                        data.put("token", returnValue);
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }
        });
        for (Flow flow : flows) {
            chain.then(flow);
        }
        chain.then(new NoRollbackFlow() {
            String __name__ = "delete_huawei_imaster_token";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String token = (String) data.get("token");
                deleteHuaweiIMasterToken(token, new Completion(trigger) {
                    @Override
                    public void success() {
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        //ignore delete toke error
                        trigger.next();
                    }
                });
            }
        }).done(new FlowDoneHandler(completion) {
            @Override
            public void handle(Map data) {
                completion.success();
            }
        }).error(new FlowErrorHandler(completion) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                completion.fail(errCode);
            }
        });

        return chain;
    }
}